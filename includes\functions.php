<?php
/**
 * Core Functions and Database Connection
 */

// Prevent direct access
if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Database Connection Class
class Database {
    private static $connection = null;
    
    public static function getConnection() {
        if (self::$connection === null) {
            try {
                self::$connection = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                    DB_USER,
                    DB_PASS,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                die("Database connection failed: " . $e->getMessage());
            }
        }
        return self::$connection;
    }
}

// Security Functions
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    if ($input === null) {
        return '';
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// URL and Routing Functions
function siteUrl($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

function themeUrl($path = '') {
    return THEME_PATH . '/' . ltrim($path, '/');
}

// Helper function to ensure URLs are absolute
function ensureAbsoluteUrl($url) {
    // If already absolute URL, return as is
    if (str_contains($url, 'http') || str_starts_with($url, '//')) {
        return $url;
    }

    // If starts with /, it's root relative, prepend domain
    if (str_starts_with($url, '/')) {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . $url;
    }

    // Otherwise, it's relative to site root
    return siteUrl($url);
}

function redirect($url) {
    header("Location: $url");
    exit;
}

function getCurrentUrl() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
           "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

// Template Functions
function loadTemplate($template, $data = []) {
    extract($data);
    $templatePath = __DIR__ . "/../templates/{$template}.php";
    if (file_exists($templatePath)) {
        include $templatePath;
    } else {
        echo "Template not found: {$template}";
    }
}

// Load Footer Component (New Modular System)
function loadFooter() {
    include __DIR__ . '/../components/footer/footer-loader.php';
    loadFooterComponent();
}

function getThemeOption($key, $default = '') {
    global $theme_options;
    
    // Try to get from database first
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT option_value FROM theme_options WHERE option_name = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    if ($result) {
        return $result['option_value'];
    }
    
    // Fallback to default theme options
    return isset($theme_options[$key]) ? $theme_options[$key] : $default;
}

function updateThemeOption($key, $value) {
    $db = Database::getConnection();
    $stmt = $db->prepare("
        INSERT INTO theme_options (option_name, option_value)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)
    ");
    return $stmt->execute([$key, $value]);
}

/**
 * Get hero section data for a specific page
 */
function getHeroSection($page_name) {
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = ? AND active = 1");
        $stmt->execute([$page_name]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return null;
    }
}

// Content Management Functions
function getSliders() {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM sliders WHERE active = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll();
}

function getServices($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM services WHERE active = 1 ORDER BY sort_order ASC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

function getServiceBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM services WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function getProjects($category = null, $limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM projects WHERE active = 1";
    $params = [];
    
    if ($category && $category !== 'all') {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

function getProjectBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM projects WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function getTestimonials($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM testimonials WHERE active = 1 ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

function getTeamMembers() {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM team_members WHERE active = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll();
}

function getBlogPosts($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM blog_posts WHERE active = 1 ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

// Navigation Menu Functions
function getNavigationMenu() {
    // Try to get from database first
    $db = Database::getConnection();
    try {
        $stmt = $db->query("SELECT * FROM navigation_menu WHERE active = 1 ORDER BY sort_order ASC");
        $menu_items = $stmt->fetchAll();
        if (!empty($menu_items)) {
            return $menu_items;
        }
    } catch (Exception $e) {
        // Table doesn't exist, use default menu
    }

    // Default navigation menu
    return [
        [
            'id' => 1,
            'title' => getThemeOption('nav_home_title', 'Home'),
            'url' => '',
            'slug' => 'home',
            'sort_order' => 1,
            'active' => 1
        ],
        [
            'id' => 2,
            'title' => getThemeOption('nav_about_title', 'About'),
            'url' => 'about',
            'slug' => 'about',
            'sort_order' => 2,
            'active' => 1
        ],
        [
            'id' => 3,
            'title' => getThemeOption('nav_work_title', 'Work'),
            'url' => 'projects',
            'slug' => 'projects',
            'sort_order' => 3,
            'active' => 1
        ],
        [
            'id' => 4,
            'title' => getThemeOption('nav_team_title', 'Team'),
            'url' => 'team',
            'slug' => 'team',
            'sort_order' => 4,
            'active' => 1
        ],
        [
            'id' => 5,
            'title' => getThemeOption('nav_services_title', 'Services'),
            'url' => 'services',
            'slug' => 'services',
            'sort_order' => 5,
            'active' => 1,
            'has_dropdown' => true
        ],
        [
            'id' => 6,
            'title' => getThemeOption('nav_news_title', 'News'),
            'url' => 'blog',
            'slug' => 'blog',
            'sort_order' => 6,
            'active' => 1
        ],
        [
            'id' => 7,
            'title' => getThemeOption('nav_contact_title', 'Contact'),
            'url' => 'contact',
            'slug' => 'contact',
            'sort_order' => 7,
            'active' => 1
        ]
    ];
}

// Footer Navigation Functions
function getFooterCompanyLinks() {
    return [
        [
            'title' => getThemeOption('footer_company_about_title', 'About Us'),
            'url' => 'about'
        ],
        [
            'title' => getThemeOption('footer_company_team_title', 'Our Team'),
            'url' => 'team'
        ],
        [
            'title' => getThemeOption('footer_company_news_title', 'News'),
            'url' => 'news'
        ],
        [
            'title' => getThemeOption('footer_company_contact_title', 'Contact'),
            'url' => 'contact'
        ]
    ];
}

function getFooterServiceLinks() {
    $services = getServices(4); // Get top 4 services
    if (!empty($services)) {
        return array_map(function($service) {
            return [
                'title' => $service['title'],
                'url' => 'service/' . $service['slug']
            ];
        }, $services);
    }

    // Default services if none in database
    return [
        [
            'title' => getThemeOption('footer_service_1_title', 'Architectural Design'),
            'url' => 'service/architectural-design'
        ],
        [
            'title' => getThemeOption('footer_service_2_title', 'Structural Engineering'),
            'url' => 'service/structural-engineering'
        ],
        [
            'title' => getThemeOption('footer_service_3_title', 'Construction Management'),
            'url' => 'service/construction-management'
        ],
        [
            'title' => getThemeOption('footer_service_4_title', 'Sustainable Design'),
            'url' => 'service/sustainable-design'
        ]
    ];
}

function getFooterProjectLinks() {
    return [
        [
            'title' => getThemeOption('footer_project_all_title', 'All Projects'),
            'url' => 'projects'
        ],
        [
            'title' => getThemeOption('footer_project_featured_title', 'Featured Work'),
            'url' => 'work'
        ],
        [
            'title' => getThemeOption('footer_project_commercial_title', 'Commercial'),
            'url' => 'projects'
        ],
        [
            'title' => getThemeOption('footer_project_residential_title', 'Residential'),
            'url' => 'projects'
        ]
    ];
}

function getBlogPostBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Email Functions
function sendEmail($to, $subject, $message, $fromEmail = null, $fromName = null) {
    $fromEmail = $fromEmail ?: FROM_EMAIL;
    $fromName = $fromName ?: FROM_NAME;

    if (SMTP_ENABLED) {
        return sendSMTPEmail($to, $subject, $message, $fromEmail, $fromName);
    } else {
        return sendBasicEmail($to, $subject, $message, $fromEmail, $fromName);
    }
}

function sendBasicEmail($to, $subject, $message, $fromEmail, $fromName) {
    $headers = [
        'From' => "$fromName <$fromEmail>",
        'Reply-To' => $fromEmail,
        'X-Mailer' => 'PHP/' . phpversion(),
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8'
    ];

    $headerString = '';
    foreach ($headers as $key => $value) {
        $headerString .= "$key: $value\r\n";
    }

    return mail($to, $subject, $message, $headerString);
}

function sendSMTPEmail($to, $subject, $message, $fromEmail, $fromName) {
    // Basic SMTP implementation - for production, consider using PHPMailer
    $socket = fsockopen(SMTP_HOST, SMTP_PORT, $errno, $errstr, 30);
    if (!$socket) {
        error_log("SMTP connection failed: $errstr ($errno)");
        return false;
    }

    // This is a simplified SMTP implementation
    // For production, use PHPMailer or similar library
    fclose($socket);
    return false; // Return false for now, implement full SMTP later
}

function getEmailTemplate($type, $data = []) {
    $templates = [
        'contact_notification' => [
            'subject' => 'New Contact Form Submission - ' . SITE_NAME,
            'body' => '
                <html>
                <head><title>New Contact Submission</title></head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #E67E22;">New Contact Form Submission</h2>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 5px;">
                            <p><strong>Name:</strong> {name}</p>
                            <p><strong>Email:</strong> <a href="mailto:{email}">{email}</a></p>
                            <p><strong>Phone:</strong> {phone}</p>
                            <p><strong>Service:</strong> {service}</p>
                            <p><strong>Message:</strong></p>
                            <div style="background: white; padding: 15px; border-left: 4px solid #E67E22;">
                                {message}
                            </div>
                        </div>
                        <p style="margin-top: 20px; font-size: 12px; color: #666;">
                            This email was sent from the contact form on ' . SITE_NAME . '
                        </p>
                    </div>
                </body>
                </html>
            '
        ],
        'contact_confirmation' => [
            'subject' => 'Thank you for contacting ' . SITE_NAME,
            'body' => '
                <html>
                <head><title>Thank You</title></head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #E67E22;">Thank You for Your Message</h2>
                        <p>Dear {name},</p>
                        <p>Thank you for contacting us. We have received your message and will get back to you as soon as possible.</p>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3>Your Message:</h3>
                            <p>{message}</p>
                        </div>
                        <p>Best regards,<br>The ' . SITE_NAME . ' Team</p>
                    </div>
                </body>
                </html>
            '
        ]
    ];

    if (!isset($templates[$type])) {
        return false;
    }

    $template = $templates[$type];

    // Replace placeholders with actual data
    foreach ($data as $key => $value) {
        $template['subject'] = str_replace('{' . $key . '}', $value, $template['subject']);
        $template['body'] = str_replace('{' . $key . '}', nl2br(htmlspecialchars($value)), $template['body']);
    }

    return $template;
}

// Contact Form Handler
function handleContactForm($data) {
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $phone = sanitizeInput($data['phone']);
    $service = sanitizeInput($data['service']);
    $message = sanitizeInput($data['message']);

    // Validation
    if (empty($name) || empty($email) || empty($message)) {
        return ['success' => false, 'message' => 'Please fill in all required fields.'];
    }

    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }

    // Check for duplicate email submissions (within last 1 hour to prevent spam)
    $db = Database::getConnection();
    $duplicate_check = $db->prepare("
        SELECT id FROM contact_submissions
        WHERE email = ? AND is_newsletter_signup = 0 AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $duplicate_check->execute([$email]);

    if ($duplicate_check->fetch()) {
        return ['success' => false, 'message' => 'You have already submitted a contact request recently. Please wait 1 hour before submitting again.'];
    }

    // Save to database
    $stmt = $db->prepare("
        INSERT INTO contact_submissions (name, email, phone, service, message, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
    ");

    try {
        $stmt->execute([$name, $email, $phone, $service, $message]);

        // Prepare email data
        $emailData = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone ?: 'Not provided',
            'service' => $service ?: 'Not specified',
            'message' => $message
        ];

        // Send admin notification email
        $adminTemplate = getEmailTemplate('contact_notification', $emailData);
        if ($adminTemplate) {
            $emailSent = sendEmail(ADMIN_EMAIL, $adminTemplate['subject'], $adminTemplate['body']);

            if (!$emailSent) {
                error_log("Failed to send admin notification email for contact form submission");
            }
        }

        // Send confirmation email to user
        $userTemplate = getEmailTemplate('contact_confirmation', $emailData);
        if ($userTemplate) {
            $confirmationSent = sendEmail($email, $userTemplate['subject'], $userTemplate['body']);

            if (!$confirmationSent) {
                error_log("Failed to send confirmation email to user: $email");
            }
        }

        return ['success' => true, 'message' => 'Thank you for your message. We will get back to you soon!'];
    } catch (PDOException $e) {
        error_log("Contact form database error: " . $e->getMessage());
        return ['success' => false, 'message' => 'There was an error sending your message. Please try again.'];
    }
}

// File Upload Helper
function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp']) {
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => 'File is larger than the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'File is larger than the MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
        ];
        $message = isset($error_messages[$file['error']]) ? $error_messages[$file['error']] : 'Unknown upload error: ' . $file['error'];
        return ['success' => false, 'message' => $message];
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File is too large. Maximum size is ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB'];
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowed_types)) {
        return ['success' => false, 'message' => 'File type not allowed. Allowed types: ' . implode(', ', $allowed_types)];
    }
    
    // Create upload directory if it doesn't exist
    if (!file_exists(UPLOAD_PATH)) {
        if (!mkdir(UPLOAD_PATH, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }
    
    // Check if upload directory is writable
    if (!is_writable(UPLOAD_PATH)) {
        return ['success' => false, 'message' => 'Upload directory is not writable'];
    }
    
    // Generate unique filename
    $filename = time() . '_' . uniqid() . '.' . $extension;
    $filepath = UPLOAD_PATH . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Ensure absolute URL for the uploaded file
        $file_url = UPLOAD_URL . $filename;
        // Double-check that we have an absolute URL
        if (!str_contains($file_url, 'http')) {
            $file_url = SITE_URL . '/' . ltrim($file_url, '/');
        }
        return ['success' => true, 'filename' => $filename, 'url' => $file_url];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file. Check permissions.'];
    }
}

// Utility Functions
function formatDate($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

function createSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

/**
 * Admin Theme System Functions
 */

/**
 * Render admin page using the theme system
 */
function renderAdminPage($template_type, $data = []) {
    // Set default values
    $defaults = [
        'page_title' => 'Admin Dashboard',
        'page_description' => '',
        'page_icon' => 'fas fa-cog',
        'show_page_header' => true,
        'body_class' => '',
        'additional_css' => [],
        'additional_js' => [],
        'inline_css' => '',
        'inline_js' => ''
    ];

    // Merge with provided data
    $data = array_merge($defaults, $data);

    // Extract variables for template
    extract($data);

    // Determine content file based on template type
    if (!isset($content_file)) {
        switch ($template_type) {
            case 'management':
                $content_file = __DIR__ . '/../admin/theme/content/management-template.php';
                break;
            case 'settings':
                $content_file = __DIR__ . '/../admin/theme/content/settings-template.php';
                break;
            case 'form':
                $content_file = __DIR__ . '/../admin/theme/content/form-template.php';
                break;
            default:
                $content_file = isset($data['content_file']) ? $data['content_file'] : null;
        }
    }

    // Include the main layout
    include __DIR__ . '/../admin/theme/layouts/admin-layout.php';
}

/**
 * Get admin theme configuration
 */
if (!function_exists('getAdminThemeConfig')) {
    function getAdminThemeConfig() {
        static $config = null;

        if ($config === null) {
            require_once __DIR__ . '/../admin/theme/config/theme-config.php';
            $config = new AdminThemeConfig();
        }

        return $config;
    }
}

/**
 * Load admin theme assets
 */
function loadAdminThemeAssets() {
    $themeConfig = getAdminThemeConfig();
    $cssVars = $themeConfig->getCSSVariables();

    // Output CSS variables
    echo '<style>:root {';
    foreach ($cssVars as $property => $value) {
        echo $property . ': ' . $value . ';';
    }
    echo '}</style>';
}

/**
 * Get admin navigation items
 */
function getAdminNavigation() {
    $current_page = basename($_SERVER['PHP_SELF']);

    return [
        [
            'title' => 'Theme Options',
            'url' => 'index.php',
            'icon' => 'fas fa-palette',
            'active' => $current_page === 'index.php'
        ],
        [
            'title' => 'Sliders',
            'url' => 'sliders.php',
            'icon' => 'fas fa-images',
            'active' => $current_page === 'sliders.php'
        ],
        [
            'title' => 'Hero Sections',
            'url' => 'hero-sections.php',
            'icon' => 'fas fa-star',
            'active' => $current_page === 'hero-sections.php'
        ],
        [
            'title' => 'Hero Headers',
            'url' => 'hero-headers.php',
            'icon' => 'fas fa-image',
            'active' => $current_page === 'hero-headers.php'
        ],
        [
            'title' => 'Services',
            'url' => 'services.php',
            'icon' => 'fas fa-tools',
            'active' => $current_page === 'services.php'
        ],
        [
            'title' => 'Projects',
            'url' => 'projects.php',
            'icon' => 'fas fa-building',
            'active' => $current_page === 'projects.php'
        ],
        [
            'title' => 'Team',
            'url' => 'team.php',
            'icon' => 'fas fa-users',
            'active' => $current_page === 'team.php'
        ],
        [
            'title' => 'Testimonials',
            'url' => 'testimonials.php',
            'icon' => 'fas fa-quote-right',
            'active' => $current_page === 'testimonials.php'
        ],
        [
            'title' => 'Blog',
            'url' => 'blog.php',
            'icon' => 'fas fa-blog',
            'active' => $current_page === 'blog.php'
        ],
        [
            'title' => 'Contact',
            'url' => 'contacts.php',
            'icon' => 'fas fa-envelope',
            'active' => $current_page === 'contacts.php'
        ],
        [
            'title' => 'Email Settings',
            'url' => 'email-settings.php',
            'icon' => 'fas fa-cog',
            'active' => $current_page === 'email-settings.php'
        ]
    ];
}

/**
 * Get hero header data for a specific page
 * @param string $page_name The page identifier
 * @return array|null Hero header data or null if not found
 */
function getHeroHeader($page_name) {
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM hero_headers WHERE page_name = ? AND active = 1");
        $stmt->execute([$page_name]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error fetching hero header for page '$page_name': " . $e->getMessage());
        return null;
    }
}

/**
 * Render service-specific hero section
 * @param array $hero_data Hero configuration data
 * @param array $service_data Service data for breadcrumbs and content
 */
function renderServiceHero($hero_data, $service_data) {
    // Background styles
    $background_style = '';
    $overlay_style = '';

    switch ($hero_data['background_type']) {
        case 'image':
            if (!empty($hero_data['background_image'])) {
                $background_style = "background-image: url('" . ensureAbsoluteUrl($hero_data['background_image']) . "');";
                $overlay_style = "background-color: " . $hero_data['background_color'] . "; opacity: " . $hero_data['background_opacity'] . ";";
            }
            break;

        case 'gradient':
            $background_style = "background: " . $hero_data['background_gradient'] . ";";
            break;

        case 'color':
            $background_style = "background-color: " . $hero_data['background_color'] . ";";
            break;
    }

    // Height and padding styles
    $height_style = '';
    switch ($hero_data['height_type']) {
        case 'small': $height_style = 'min-height: 300px;'; break;
        case 'medium': $height_style = 'min-height: 400px;'; break;
        case 'large': $height_style = 'min-height: 500px;'; break;
        case 'custom': $height_style = 'min-height: ' . $hero_data['height_custom'] . 'px;'; break;
    }

    $padding_style = "padding-top: " . $hero_data['padding_top'] . "; padding-bottom: " . $hero_data['padding_bottom'] . ";";

    // Current page title for breadcrumbs
    $current_page_title = $hero_data['page_title'];

    // Colors
    $title_color = $hero_data['title_color'];
    $subtitle_color = $hero_data['subtitle_color'];
    $breadcrumb_color = $hero_data['breadcrumb_color'];

    ?>
    <section class="service-hero-header" style="<?php echo $background_style . $height_style . $padding_style; ?>">
        <?php if ($hero_data['background_type'] === 'image' && $overlay_style): ?>
            <div class="service-hero-overlay" style="<?php echo $overlay_style; ?>"></div>
        <?php endif; ?>

        <div class="container">
            <div class="service-hero-content">
                <?php if ($hero_data['show_breadcrumbs']): ?>
                    <nav class="service-breadcrumb" style="color: <?php echo $breadcrumb_color; ?>;">
                        <a href="<?php echo siteUrl(); ?>" style="color: <?php echo $breadcrumb_color; ?>;">Home</a>
                        <span class="breadcrumb-separator">›</span>
                        <a href="<?php echo siteUrl('services.php'); ?>" style="color: <?php echo $breadcrumb_color; ?>;">Services</a>
                        <span class="breadcrumb-separator">›</span>
                        <span class="current-page"><?php echo htmlspecialchars($current_page_title); ?></span>
                    </nav>
                <?php endif; ?>

                <h1 class="service-hero-title" style="color: <?php echo $title_color; ?>;">
                    <?php echo htmlspecialchars($hero_data['page_title']); ?>
                </h1>

                <?php if (!empty($hero_data['subtitle'])): ?>
                    <p class="service-hero-subtitle" style="color: <?php echo $subtitle_color; ?>;">
                        <?php echo htmlspecialchars($hero_data['subtitle']); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <style>
    /* Service Hero Header Styles */
    .service-hero-header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        color: white;
        text-align: center;
    }

    .service-hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .service-hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .service-breadcrumb {
        font-size: 0.9rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .service-breadcrumb a {
        text-decoration: none;
        transition: opacity 0.3s ease;
    }

    .service-breadcrumb a:hover {
        opacity: 0.8;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
        opacity: 0.7;
    }

    .current-page {
        opacity: 0.8;
    }

    .service-hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .service-hero-subtitle {
        font-size: 1.2rem;
        font-weight: 400;
        margin-bottom: 0;
        opacity: 0.9;
        line-height: 1.5;
    }

    @media (max-width: 768px) {
        .service-hero-title {
            font-size: 2rem;
        }

        .service-hero-subtitle {
            font-size: 1rem;
        }

        .service-hero-content {
            padding: 1.5rem 1rem;
        }
    }
    </style>
    <?php
}

/**
 * Sync service hero headers - create/update hero headers for all services
 * This ensures each service has a dedicated hero header entry
 */
function syncServiceHeroHeaders() {
    try {
        $db = Database::getConnection();

        // Get all active services
        $stmt = $db->prepare("SELECT * FROM services WHERE active = 1 ORDER BY sort_order ASC");
        $stmt->execute();
        $services = $stmt->fetchAll();

        foreach ($services as $service) {
            $page_name = 'service-details-' . $service['slug'];

            // Check if hero header already exists
            $stmt = $db->prepare("SELECT id FROM hero_headers WHERE page_name = ?");
            $stmt->execute([$page_name]);
            $existing = $stmt->fetch();

            $hero_title = !empty($service['hero_title']) ? $service['hero_title'] : $service['title'];
            $hero_subtitle = !empty($service['hero_subtitle']) ? $service['hero_subtitle'] : $service['description'];
            $background_image = !empty($service['featured_background']) ? $service['featured_background'] : '';
            $background_type = !empty($background_image) ? 'image' : 'gradient';

            if ($existing) {
                // Update existing hero header
                $stmt = $db->prepare("
                    UPDATE hero_headers SET
                        page_title = ?,
                        subtitle = ?,
                        background_type = ?,
                        background_image = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE page_name = ?
                ");
                $stmt->execute([$hero_title, $hero_subtitle, $background_type, $background_image, $page_name]);
            } else {
                // Create new hero header
                $stmt = $db->prepare("
                    INSERT INTO hero_headers (
                        page_name, page_title, subtitle, show_breadcrumbs,
                        background_type, background_image, background_gradient, background_color, background_opacity,
                        height_type, height_custom, padding_top, padding_bottom,
                        title_color, subtitle_color, breadcrumb_color,
                        show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                        active, created_at, updated_at
                    ) VALUES (?, ?, ?, 1, ?, ?, ?, ?, 0.7, 'medium', 400, '4rem', '4rem', '#ffffff', '#ffffff', '#ffffff', 0, '', '', '#E67E22', '#ffffff', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ");
                $stmt->execute([
                    $page_name,
                    $hero_title,
                    $hero_subtitle,
                    $background_type,
                    $background_image,
                    'linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(230, 126, 34, 0.6) 100%)',
                    '#1A1A1A'
                ]);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Error syncing service hero headers: " . $e->getMessage());
        return false;
    }
}

/**
 * Get service by slug with all hero data
 * @param string $slug Service slug
 * @return array|null Service data with hero information
 */
function getServiceBySlugWithHero($slug) {
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM services WHERE slug = ? AND active = 1");
        $stmt->execute([$slug]);
        $service = $stmt->fetch();

        if ($service) {
            // Sync hero headers to ensure this service has one
            syncServiceHeroHeaders();
        }

        return $service;
    } catch (Exception $e) {
        error_log("Error fetching service with hero data: " . $e->getMessage());
        return null;
    }
}

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
