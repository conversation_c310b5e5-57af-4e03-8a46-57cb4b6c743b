<?php
/**
 * Contact Submissions - Admin Panel
 * Now using the new Admin Theme System
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle actions
$message = '';
$error = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        try {
            $db = Database::getConnection();

            switch ($_POST['action']) {
                case 'mark_read':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("UPDATE contact_submissions SET status = 'read' WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission marked as read.';
                    }
                    break;

                case 'mark_replied':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("UPDATE contact_submissions SET status = 'replied' WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission marked as replied.';
                    }
                    break;

                case 'delete':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("DELETE FROM contact_submissions WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission deleted.';
                    }
                    break;

                case 'delete_all':
                    if (isset($_POST['confirm_delete_all']) && $_POST['confirm_delete_all'] === 'yes') {
                        $stmt = $db->prepare("DELETE FROM contact_submissions");
                        $stmt->execute();
                        $deletedCount = $stmt->rowCount();
                        $message = "All contact submissions deleted successfully. ({$deletedCount} entries removed)";
                    } else {
                        $error = 'Delete all action requires confirmation.';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error = 'Error processing request: ' . $e->getMessage();
        }
    }
}

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$per_page = in_array($per_page, [10, 20, 50, 100]) ? $per_page : 20;
$offset = ($page - 1) * $per_page;

// Get search parameter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Get contact submissions with pagination
try {
    $db = Database::getConnection();

    // Build search query
    $where_clause = '';
    $params = [];
    if ($search) {
        $where_clause = "WHERE name LIKE ? OR email LIKE ? OR message LIKE ?";
        $search_param = "%{$search}%";
        $params = [$search_param, $search_param, $search_param];
    }

    // Get total count for pagination
    $count_stmt = $db->prepare("SELECT COUNT(*) FROM contact_submissions {$where_clause}");
    $count_stmt->execute($params);
    $total_items = $count_stmt->fetchColumn();

    // Get paginated results
    $stmt = $db->prepare("SELECT * FROM contact_submissions {$where_clause} ORDER BY created_at DESC LIMIT ? OFFSET ?");
    $stmt->execute(array_merge($params, [$per_page, $offset]));
    $submissions = $stmt->fetchAll();

    // Get all submissions for statistics (without pagination)
    $all_stmt = $db->prepare("SELECT * FROM contact_submissions");
    $all_stmt->execute();
    $all_submissions = $all_stmt->fetchAll();

    // Get statistics
    $stats = [
        'total' => count($all_submissions),
        'unread' => count(array_filter($all_submissions, fn($s) => ($s['status'] ?? 'unread') === 'unread')),
        'read' => count(array_filter($all_submissions, fn($s) => ($s['status'] ?? 'unread') === 'read')),
        'replied' => count(array_filter($all_submissions, fn($s) => ($s['status'] ?? 'unread') === 'replied')),
        'newsletter' => count(array_filter($all_submissions, fn($s) => !empty($s['is_newsletter_signup']))),
        'contacts' => count(array_filter($all_submissions, fn($s) => empty($s['is_newsletter_signup'])))
    ];
} catch (Exception $e) {
    $error = 'Error loading submissions: ' . $e->getMessage();
    $submissions = [];
    $stats = ['total' => 0, 'unread' => 0, 'read' => 0, 'replied' => 0];
}

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Contact Submissions',
    'page_icon' => 'fas fa-envelope',
    'page_description' => 'Manage and respond to contact form submissions from your website.',
    'management_title' => 'Contact Submissions Management',
    'management_description' => 'View, organize, and respond to customer inquiries and contact form submissions.',
    'show_search' => true,
    'search_placeholder' => 'Search submissions by name, email, or message...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => "Contact Submissions (Page {$page} of " . ceil($total_items / $per_page) . ")",
    'table_content_file' => __DIR__ . '/theme/content/contacts-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-envelope',
            'number' => $stats['total'],
            'label' => 'Total Submissions'
        ],
        [
            'icon' => 'fas fa-phone',
            'number' => $stats['contacts'],
            'label' => 'Contact Forms'
        ],
        [
            'icon' => 'fas fa-newspaper',
            'number' => $stats['newsletter'],
            'label' => 'Newsletter Signups'
        ],
        [
            'icon' => 'fas fa-envelope-open',
            'number' => $stats['unread'],
            'label' => 'Unread'
        ],
        [
            'icon' => 'fas fa-eye',
            'number' => $stats['read'],
            'label' => 'Read'
        ],
        [
            'icon' => 'fas fa-reply',
            'number' => $stats['replied'],
            'label' => 'Replied'
        ]
    ],
    'message' => $message,
    'error' => $error,
    'submissions' => $submissions,
    'total_items' => $total_items,
    'per_page' => $per_page,
    'current_page' => $page,
    'search' => $search
]);
?>

