<?php
/**
 * Contact Submissions - Admin Panel
 * Now using the new Admin Theme System
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle actions
$message = '';
$error = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        try {
            $db = Database::getConnection();

            switch ($_POST['action']) {
                case 'mark_read':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("UPDATE contact_submissions SET status = 'read' WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission marked as read.';
                    }
                    break;

                case 'mark_replied':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("UPDATE contact_submissions SET status = 'replied' WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission marked as replied.';
                    }
                    break;

                case 'delete':
                    if (isset($_POST['submission_id'])) {
                        $stmt = $db->prepare("DELETE FROM contact_submissions WHERE id = ?");
                        $stmt->execute([$_POST['submission_id']]);
                        $message = 'Submission deleted.';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error = 'Error processing request: ' . $e->getMessage();
        }
    }
}

// Get all contact submissions
try {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM contact_submissions ORDER BY created_at DESC");
    $stmt->execute();
    $submissions = $stmt->fetchAll();

    // Get statistics
    $stats = [
        'total' => count($submissions),
        'unread' => count(array_filter($submissions, fn($s) => ($s['status'] ?? 'unread') === 'unread')),
        'read' => count(array_filter($submissions, fn($s) => ($s['status'] ?? 'unread') === 'read')),
        'replied' => count(array_filter($submissions, fn($s) => ($s['status'] ?? 'unread') === 'replied')),
        'newsletter' => count(array_filter($submissions, fn($s) => !empty($s['is_newsletter_signup']))),
        'contacts' => count(array_filter($submissions, fn($s) => empty($s['is_newsletter_signup'])))
    ];
} catch (Exception $e) {
    $error = 'Error loading submissions: ' . $e->getMessage();
    $submissions = [];
    $stats = ['total' => 0, 'unread' => 0, 'read' => 0, 'replied' => 0];
}

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Contact Submissions',
    'page_icon' => 'fas fa-envelope',
    'page_description' => 'Manage and respond to contact form submissions from your website.',
    'management_title' => 'Contact Submissions Management',
    'management_description' => 'View, organize, and respond to customer inquiries and contact form submissions.',
    'show_search' => true,
    'search_placeholder' => 'Search submissions by name, email, or message...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Contact Submissions',
    'table_content_file' => __DIR__ . '/theme/content/contacts-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-envelope',
            'number' => $stats['total'],
            'label' => 'Total Submissions'
        ],
        [
            'icon' => 'fas fa-phone',
            'number' => $stats['contacts'],
            'label' => 'Contact Forms'
        ],
        [
            'icon' => 'fas fa-newspaper',
            'number' => $stats['newsletter'],
            'label' => 'Newsletter Signups'
        ],
        [
            'icon' => 'fas fa-envelope-open',
            'number' => $stats['unread'],
            'label' => 'Unread'
        ],
        [
            'icon' => 'fas fa-eye',
            'number' => $stats['read'],
            'label' => 'Read'
        ],
        [
            'icon' => 'fas fa-reply',
            'number' => $stats['replied'],
            'label' => 'Replied'
        ]
    ],
    'message' => $message,
    'error' => $error,
    'submissions' => $submissions
]);
?>

