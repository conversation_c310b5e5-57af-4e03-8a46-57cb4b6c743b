<?php
/**
 * Hero Headers Admin Interface
 * Manages page header hero sections across the site
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

/**
 * Handle file upload for background images
 */
function handleImageUpload($file, $old_image = null) {
    if (!isset($file) || $file['error'] === UPLOAD_ERR_NO_FILE) {
        // No new file uploaded, keep existing
        $relative_path = $old_image ?: '';
        $absolute_url = $relative_path ? (str_starts_with($relative_path, 'http') ? $relative_path : SITE_URL . '/' . ltrim($relative_path, '/')) : '';
        return [
            'db_path' => $relative_path,
            'preview_url' => $absolute_url
        ];
    }

    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $file['error']);
    }

    // Validate file type
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    $file_type = mime_content_type($file['tmp_name']);

    if (!in_array($file_type, $allowed_types)) {
        throw new Exception('Invalid file type. Only JPG, PNG, and WebP images are allowed.');
    }

    // Validate file size (5MB max)
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        throw new Exception('File too large. Maximum size is 5MB.');
    }

    // Create upload directory if it doesn't exist
    $upload_dir = dirname(__DIR__) . '/assets/images/hero-headers';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'hero-' . uniqid() . '.' . $extension;
    $upload_path = $upload_dir . '/' . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        throw new Exception('Failed to save uploaded file.');
    }

    // Delete old image if it exists and is a local file
    if ($old_image && strpos($old_image, 'assets/images/hero-headers/') !== false) {
        $old_path = dirname(__DIR__) . '/' . $old_image;
        if (file_exists($old_path)) {
            unlink($old_path);
        }
    }

    // Return absolute URL for preview and relative path for DB
    $relative_path = 'assets/images/hero-headers/' . $filename;
    $absolute_url = SITE_URL . '/' . $relative_path;
    // Store relative path in DB, but use absolute URL for preview
    return [
        'db_path' => $relative_path,
        'preview_url' => $absolute_url
    ];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = Database::getConnection();

        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    // Handle image upload
                    $background_image = '';
                    if (isset($_FILES['background_image_upload'])) {
                        $upload_result = handleImageUpload($_FILES['background_image_upload']);
                        $background_image = $upload_result['db_path'];
                        $background_image_preview = $upload_result['preview_url'];
                    } else {
                        $background_image_preview = '';
                    }

                    // Check for duplicate page_name
                    $dup_stmt = $db->prepare("SELECT COUNT(*) FROM hero_headers WHERE page_name = ?");
                    $dup_stmt->execute([$_POST['page_name']]);
                    $exists = $dup_stmt->fetchColumn();
                    if ($exists) {
                        $error = 'A hero header for this page already exists. Please edit it instead.';
                        break;
                    }

                    $stmt = $db->prepare("
                        INSERT INTO hero_headers (
                            page_name, page_title, subtitle, show_breadcrumbs,
                            background_type, background_image, background_gradient, background_color, background_opacity,
                            height_type, height_custom, padding_top, padding_bottom, title_color, title_font_size, subtitle_color, breadcrumb_color,
                            show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                            active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['page_name'], $_POST['page_title'], $_POST['subtitle'],
                        isset($_POST['show_breadcrumbs']) ? 1 : 0,
                        $_POST['background_type'], $background_image, $_POST['background_gradient'],
                        $_POST['background_color'], $_POST['background_opacity'],
                        $_POST['height_type'], $_POST['height_custom'],
                        $_POST['padding_top'] ?? '4rem', $_POST['padding_bottom'] ?? '4rem',
                        $_POST['title_color'], $_POST['title_font_size'] ?? '3rem', $_POST['subtitle_color'], $_POST['breadcrumb_color'],
                        isset($_POST['show_cta_button']) ? 1 : 0, $_POST['cta_button_text'], $_POST['cta_button_link'],
                        $_POST['cta_button_color'], $_POST['cta_button_text_color'],
                        isset($_POST['active']) ? 1 : 0
                    ]);

                    $message = 'Hero header created successfully!';
                    break;
                    
                case 'update':
                    // Get current image for potential replacement
                    $current_stmt = $db->prepare("SELECT background_image FROM hero_headers WHERE id = ?");
                    $current_stmt->execute([$_POST['id']]);
                    $current_data = $current_stmt->fetch();
                    $current_image = $current_data['background_image'] ?? '';

                    // Handle image upload or removal
                    $background_image = $current_image;

                    if (isset($_POST['remove_image']) && $_POST['remove_image']) {
                        // Remove current image
                        if ($current_image && strpos($current_image, 'assets/images/hero-headers/') !== false) {
                            $old_path = dirname(__DIR__) . '/' . $current_image;
                            if (file_exists($old_path)) {
                                unlink($old_path);
                            }
                        }
                        $background_image = '';
                    } elseif (isset($_FILES['background_image_upload'])) {
                        // Handle new image upload
                        $upload_result = handleImageUpload($_FILES['background_image_upload'], $current_image);
                        $background_image = $upload_result['db_path'];
                        $background_image_preview = $upload_result['preview_url'];
                    } else {
                        $background_image_preview = '';
                    }

                    $stmt = $db->prepare("
                        UPDATE hero_headers SET
                            page_title = ?, subtitle = ?, show_breadcrumbs = ?,
                            background_type = ?, background_image = ?, background_gradient = ?,
                            background_color = ?, background_opacity = ?,
                            height_type = ?, height_custom = ?, padding_top = ?, padding_bottom = ?,
                            title_color = ?, title_font_size = ?, subtitle_color = ?, breadcrumb_color = ?,
                            show_cta_button = ?, cta_button_text = ?, cta_button_link = ?,
                            cta_button_color = ?, cta_button_text_color = ?,
                            active = ?, updated_at = NOW()
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['page_title'], $_POST['subtitle'],
                        isset($_POST['show_breadcrumbs']) ? 1 : 0,
                        $_POST['background_type'], $background_image, $_POST['background_gradient'],
                        $_POST['background_color'], $_POST['background_opacity'],
                        $_POST['height_type'], $_POST['height_custom'],
                        $_POST['padding_top'] ?? '4rem', $_POST['padding_bottom'] ?? '4rem',
                        $_POST['title_color'], $_POST['title_font_size'] ?? '3rem', $_POST['subtitle_color'], $_POST['breadcrumb_color'],
                        isset($_POST['show_cta_button']) ? 1 : 0, $_POST['cta_button_text'], $_POST['cta_button_link'],
                        $_POST['cta_button_color'], $_POST['cta_button_text_color'],
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['id']
                    ]);

                    $message = 'Hero header updated successfully!';
                    break;

                case 'delete':
                    $stmt = $db->prepare("DELETE FROM hero_headers WHERE id = ?");
                    $stmt->execute([$_POST['id']]);

                    $message = 'Hero header deleted successfully!';
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get all hero headers
$db = Database::getConnection();
$stmt = $db->query("SELECT * FROM hero_headers ORDER BY page_name ASC");
$hero_headers = $stmt->fetchAll();

// Get specific hero header for editing
$edit_hero_header = null;
if (isset($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM hero_headers WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_hero_header = $stmt->fetch();
}

// Calculate statistics
$total_headers = count($hero_headers);
$active_headers = count(array_filter($hero_headers, function($h) { return $h['active']; }));
$inactive_headers = $total_headers - $active_headers;
$headers_with_cta = count(array_filter($hero_headers, function($h) { return $h['show_cta_button']; }));

// Render the page using the admin theme system
renderAdminPage('management', [
    'page_title' => 'Hero Headers Management',
    'page_icon' => 'fas fa-image',
    'page_description' => 'Manage page header hero sections with titles, breadcrumbs, backgrounds, and call-to-action buttons.',
    'management_title' => 'Hero Headers',
    'management_description' => 'Create and customize page header sections that appear at the top of pages with professional styling and flexible content options.',
    'management_actions' => [
        [
            'url' => '#',
            'label' => 'Add New Hero Header',
            'class' => 'btn-primary',
            'icon' => 'fas fa-plus',
            'onclick' => 'toggleAddForm()'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search hero headers by page name, title, or subtitle...',
    'search_target' => '.searchable-item',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-image',
            'number' => $total_headers,
            'label' => 'Total Headers'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $active_headers,
            'label' => 'Active Headers'
        ],
        [
            'icon' => 'fas fa-times-circle',
            'number' => $inactive_headers,
            'label' => 'Inactive Headers'
        ],
        [
            'icon' => 'fas fa-mouse-pointer',
            'number' => $headers_with_cta,
            'label' => 'With CTA Button'
        ]
    ],
    'show_table' => true,
    'table_title' => 'All Hero Headers',
    'table_content_file' => __DIR__ . '/theme/content/hero-headers-table.php',
    'custom_content_before_table' => function() use ($edit_hero_header) {
        include __DIR__ . '/theme/content/hero-headers-form.php';
    },
    'message' => $message,
    'error' => $error,
    'hero_headers' => $hero_headers,
    'edit_hero_header' => $edit_hero_header,
    'custom_js' => '
        function toggleAddForm() {
            const form = document.getElementById("heroHeaderForm");
            if (form) {
                form.style.display = form.style.display === "none" ? "block" : "none";
                if (form.style.display === "block") {
                    document.getElementById("page_name").focus();
                }
            }
        }
    '
]);
?>
