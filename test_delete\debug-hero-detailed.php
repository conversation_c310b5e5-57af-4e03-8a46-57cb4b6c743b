<?php
define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

echo "🔍 DETAILED HERO DEBUG FOR ARCHITECTURAL DESIGN\n";
echo "===============================================\n\n";

$service_slug = 'architectural-design';
$hero_page_name = 'service-details-' . $service_slug;

echo "Service slug: $service_slug\n";
echo "Hero page name: $hero_page_name\n\n";

// Check if service exists
$service = getServiceBySlug($service_slug);
if ($service) {
    echo "✅ Service found:\n";
    echo "   - ID: " . $service['id'] . "\n";
    echo "   - Title: " . $service['title'] . "\n";
    echo "   - Hero Title: " . ($service['hero_title'] ?: 'Empty') . "\n";
    echo "   - Hero Subtitle: " . ($service['hero_subtitle'] ?: 'Empty') . "\n";
    echo "   - Featured Background: " . ($service['featured_background'] ?: 'Empty') . "\n";
    echo "   - Active: " . ($service['active'] ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ Service not found!\n";
    exit;
}

echo "\n";

// Check if hero header exists
$hero = getHeroHeader($hero_page_name);
if ($hero) {
    echo "✅ Hero header found:\n";
    echo "   - ID: " . $hero['id'] . "\n";
    echo "   - Page Name: " . $hero['page_name'] . "\n";
    echo "   - Page Title: " . $hero['page_title'] . "\n";
    echo "   - Subtitle: " . ($hero['subtitle'] ?: 'Empty') . "\n";
    echo "   - Background Type: " . $hero['background_type'] . "\n";
    echo "   - Background Image: " . ($hero['background_image'] ?: 'Empty') . "\n";
    echo "   - Show Breadcrumbs: " . ($hero['show_breadcrumbs'] ? 'Yes' : 'No') . "\n";
    echo "   - Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ Hero header not found!\n";
    echo "Running sync to create it...\n";
    
    $sync_result = syncServiceHeroHeaders();
    if ($sync_result) {
        echo "✅ Sync completed\n";
        
        $hero = getHeroHeader($hero_page_name);
        if ($hero) {
            echo "✅ Hero header created:\n";
            echo "   - Page Title: " . $hero['page_title'] . "\n";
            echo "   - Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
        } else {
            echo "❌ Hero header still not found after sync\n";
        }
    } else {
        echo "❌ Sync failed\n";
    }
}

echo "\n";

// Check all service hero headers
echo "📋 ALL SERVICE HERO HEADERS:\n";
echo "----------------------------\n";

try {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT page_name, page_title, active FROM hero_headers WHERE page_name LIKE 'service-details-%' ORDER BY page_name");
    $stmt->execute();
    $heroes = $stmt->fetchAll();
    
    if ($heroes) {
        foreach ($heroes as $h) {
            $status = $h['active'] ? '✅' : '❌';
            echo "$status " . $h['page_name'] . " → " . $h['page_title'] . "\n";
        }
    } else {
        echo "No service hero headers found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
