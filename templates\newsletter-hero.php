<?php
/**
 * Newsletter Hero Template
 * Hero section with email input for newsletter signup
 * Used on news and news-details pages
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Get hero section from database
$page_name = basename($_SERVER['PHP_SELF'], '.php');
$hero = getHeroSection($page_name);

if (!$hero || !$hero['active'] || !$hero['show_newsletter_input']) {
    return; // Don't show if no hero or newsletter not enabled
}

// Default background styles
$background_style = '';
if ($hero['background_type'] === 'image' && !empty($hero['background_image'])) {
    $background_style = "background-image: url('" . ensureAbsoluteUrl($hero['background_image']) . "');";
} elseif ($hero['background_type'] === 'gradient' && !empty($hero['background_gradient'])) {
    $background_style = "background: " . $hero['background_gradient'] . ";";
}

// Height settings
$height_class = '';
$height_style = '';
switch($hero['height_type'] ?? 'medium') {
    case 'small': $height_style = 'min-height: 300px;'; break;
    case 'medium': $height_style = 'min-height: 400px;'; break;
    case 'large': $height_style = 'min-height: 600px;'; break;
    case 'custom': $height_style = 'min-height: ' . ($hero['height_custom'] ?? 400) . 'px;'; break;
    default: $height_style = 'min-height: 400px;';
}

// Color settings
$caption_color = $hero['caption_color'] ?? '#ffffff';
$title_color = $hero['title_color'] ?? '#ffffff';
$description_color = $hero['description_color'] ?? '#ffffff';
?>

<section class="newsletter-hero" style="<?php echo $background_style . $height_style; ?>">
    <div class="hero-overlay" style="background-color: <?php echo $hero['background_color'] ?? 'rgba(0,0,0,0.5)'; ?>; opacity: <?php echo $hero['background_opacity'] ?? 0.7; ?>;"></div>
    
    <div class="container">
        <div class="newsletter-hero-content">
            <?php if (!empty($hero['caption'])): ?>
                <div class="hero-caption" style="color: <?php echo $caption_color; ?>;">
                    <?php echo htmlspecialchars($hero['caption']); ?>
                </div>
            <?php endif; ?>
            
            <h1 class="hero-title" style="color: <?php echo $title_color; ?>;">
                <?php echo htmlspecialchars($hero['title']); ?>
            </h1>
            
            <?php if (!empty($hero['description'])): ?>
                <p class="hero-description" style="color: <?php echo $description_color; ?>;">
                    <?php echo htmlspecialchars($hero['description']); ?>
                </p>
            <?php endif; ?>
            
            <!-- Newsletter Signup Form -->
            <form class="newsletter-hero-form" id="newsletterHeroForm" method="POST" action="<?php echo siteUrl('newsletter-signup'); ?>">
                <div class="newsletter-input-group">
                    <input type="email"
                           name="email"
                           class="newsletter-hero-input"
                           placeholder="<?php echo htmlspecialchars($hero['newsletter_placeholder'] ?? 'Enter your email address'); ?>"
                           required>
                    <button type="submit" class="newsletter-hero-button">
                        <?php echo htmlspecialchars($hero['newsletter_button_text'] ?? 'Subscribe'); ?>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M5 12h14M12 5l7 7-7 7"/>
                        </svg>
                    </button>
                </div>
                <div class="newsletter-success-message" id="newsletterSuccessMessage" style="display: none;">
                    <?php echo htmlspecialchars($hero['newsletter_success_message'] ?? 'Thank you for subscribing!'); ?>
                </div>
                <input type="hidden" name="source" value="newsletter_hero">
                <input type="hidden" name="page" value="<?php echo basename($_SERVER['PHP_SELF'], '.php'); ?>">
            </form>
        </div>
    </div>
</section>

<style>
/* Newsletter Hero Styles */
.newsletter-hero {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    text-align: center;
}

.newsletter-hero .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.newsletter-hero-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-hero .hero-caption {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.newsletter-hero .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.newsletter-hero .hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.newsletter-hero-form {
    max-width: 450px;
    margin: 0 auto;
}

.newsletter-input-group {
    display: flex;
    gap: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    padding: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-hero-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: white;
    font-size: 16px;
    border-radius: 50px;
    outline: none;
}

.newsletter-hero-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-hero-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--accent-color, #E67E22);
    color: white;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.newsletter-hero-button:hover {
    background: #d35400;
    transform: translateX(2px);
}

.newsletter-success-message {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(46, 204, 113, 0.9);
    color: white;
    border-radius: 8px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .newsletter-hero .hero-title {
        font-size: 2.5rem;
    }
    
    .newsletter-hero .hero-description {
        font-size: 1.1rem;
    }
    
    .newsletter-input-group {
        flex-direction: column;
        border-radius: 12px;
        gap: 8px;
    }
    
    .newsletter-hero-input,
    .newsletter-hero-button {
        border-radius: 8px;
    }
    
    .newsletter-hero-button {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('newsletterHeroForm');
    const successMessage = document.getElementById('newsletterSuccessMessage');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const formData = new FormData(this);

            if (email) {
                // Send AJAX request to newsletter signup
                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hide form and show success message
                        this.style.display = 'none';
                        successMessage.style.display = 'block';
                        successMessage.textContent = data.message;

                        // Reset form after 3 seconds
                        setTimeout(() => {
                            this.style.display = 'block';
                            successMessage.style.display = 'none';
                            this.reset();
                        }, 3000);
                    } else {
                        // Show error message
                        alert(data.message || 'An error occurred. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Newsletter signup error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        });
    }
});
</script>
