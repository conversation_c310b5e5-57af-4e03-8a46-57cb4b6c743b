<?php
$url = "http://localhost/monolith-design/service-details?service=architectural-design";
$html = file_get_contents($url);

// Find the section with "Service Details"
$lines = explode("\n", $html);
$found_section = false;
$section_lines = [];

foreach ($lines as $i => $line) {
    if (strpos($line, 'Service Details') !== false) {
        // Get context around this line
        $start = max(0, $i - 5);
        $end = min(count($lines) - 1, $i + 5);
        
        echo "Found 'Service Details' at line " . ($i + 1) . ":\n";
        echo "Context:\n";
        for ($j = $start; $j <= $end; $j++) {
            $marker = ($j == $i) ? ">>> " : "    ";
            echo $marker . ($j + 1) . ": " . trim($lines[$j]) . "\n";
        }
        echo "\n";
    }
}
?>
