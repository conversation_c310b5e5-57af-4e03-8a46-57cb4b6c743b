<?php
define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

echo "🔍 CHECKING HERO HEADER DATA\n";
echo "============================\n\n";

$hero_page_name = 'service-details-architectural-design';

try {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM hero_headers WHERE page_name = ?");
    $stmt->execute([$hero_page_name]);
    $hero = $stmt->fetch();
    
    if ($hero) {
        echo "✅ Hero header found:\n";
        foreach ($hero as $key => $value) {
            echo "   $key: " . ($value ?: 'NULL/Empty') . "\n";
        }
    } else {
        echo "❌ No hero header found for: $hero_page_name\n";
    }
    
    echo "\n";
    
    // Also check the service data
    $stmt = $db->prepare("SELECT * FROM services WHERE slug = 'architectural-design'");
    $stmt->execute();
    $service = $stmt->fetch();
    
    if ($service) {
        echo "✅ Service data:\n";
        echo "   title: " . $service['title'] . "\n";
        echo "   hero_title: " . ($service['hero_title'] ?: 'Empty') . "\n";
        echo "   hero_subtitle: " . ($service['hero_subtitle'] ?: 'Empty') . "\n";
        echo "   featured_background: " . ($service['featured_background'] ?: 'Empty') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
