<?php
/**
 * Newsletter Signup Handler - Simplified Version
 * Handles newsletter subscriptions with better error handling
 */

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to user
ini_set('log_errors', 1);

// Set content type for JSON response
header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => 'Invalid request'
];

try {
    // Check if this is a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $response['message'] = 'Only POST requests are allowed.';
        echo json_encode($response);
        exit;
    }

    // Check if it's an AJAX request
    $is_ajax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

    if (!$is_ajax) {
        $response['message'] = 'AJAX requests only.';
        echo json_encode($response);
        exit;
    }

    // Include required files
    define('MONOLITH_ACCESS', true);
    require_once __DIR__ . '/config.php';
    require_once __DIR__ . '/includes/functions.php';

    // Get and validate email
    $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
    $source = isset($_POST['source']) ? trim($_POST['source']) : 'unknown';
    $page = isset($_POST['page']) ? trim($_POST['page']) : 'unknown';

    // Validate email
    if (!$email) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit;
    }

    // Get database connection
    $db = Database::getConnection();

    // Check if newsletter_subscribers table exists, create if not
    $stmt = $db->prepare("SHOW TABLES LIKE 'newsletter_subscribers'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        // Create table
        $create_sql = "
            CREATE TABLE newsletter_subscribers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                source VARCHAR(100) DEFAULT 'unknown',
                page VARCHAR(100) DEFAULT 'unknown',
                subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive', 'unsubscribed') DEFAULT 'active',
                INDEX idx_email (email),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $db->exec($create_sql);
    }

    // Check if email already exists
    $stmt = $db->prepare("SELECT id FROM newsletter_subscribers WHERE email = ?");
    $stmt->execute([$email]);

    if ($stmt->fetch()) {
        // Already subscribed, but don't reveal this
        $response['success'] = true;
        $response['message'] = 'Thank you for subscribing!';
    } else {
        // Insert new subscriber
        $stmt = $db->prepare("
            INSERT INTO newsletter_subscribers (email, source, page, subscribed_at, status)
            VALUES (?, ?, ?, NOW(), 'active')
        ");

        if ($stmt->execute([$email, $source, $page])) {
            $response['success'] = true;
            $response['message'] = 'Thank you for subscribing!';

            // Log the subscription
            error_log("Newsletter subscription: $email from $source on $page");
        } else {
            $response['message'] = 'Sorry, there was an error. Please try again.';
        }
    }

} catch (Exception $e) {
    error_log("Newsletter signup error: " . $e->getMessage());
    $response['message'] = 'Sorry, there was an error. Please try again.';
}

// Return JSON response
echo json_encode($response);
exit;

?>
