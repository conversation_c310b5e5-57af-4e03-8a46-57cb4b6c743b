<?php
/**
 * Newsletter Signup Handler - Root Level
 * Handles newsletter subscriptions from all sources (footer, hero CTA, etc.)
 * Supports both AJAX and regular form submissions
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Initialize response
$response = [
    'success' => false,
    'message' => 'Invalid request'
];

// Debug logging
error_log("Newsletter signup accessed - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("POST data: " . print_r($_POST, true));

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get and validate email
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        $source = sanitizeInput($_POST['source'] ?? 'footer');
        $page = sanitizeInput($_POST['page'] ?? basename($_SERVER['HTTP_REFERER'] ?? 'unknown', '.php'));
        
        // Validate email
        if (!$email) {
            $response['message'] = 'Please enter a valid email address.';
        } else {
            $db = Database::getConnection();
            
            // Check if email already exists in newsletter subscribers
            $stmt = $db->prepare("SELECT id FROM newsletter_subscribers WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $response['success'] = true; // Don't reveal if already subscribed
                $response['message'] = 'Thank you for subscribing!';
            } else {
                // Insert new subscriber
                $stmt = $db->prepare("
                    INSERT INTO newsletter_subscribers (email, source, page, subscribed_at, status) 
                    VALUES (?, ?, ?, NOW(), 'active')
                ");
                
                if ($stmt->execute([$email, $source, $page])) {
                    // Also add to contact submissions for unified management
                    $contact_stmt = $db->prepare("
                        INSERT INTO contact_submissions (name, email, phone, service, message, is_newsletter_signup, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ");
                    
                    $contact_stmt->execute([
                        'Newsletter Subscriber',
                        $email,
                        '',
                        'Newsletter Subscription',
                        "Newsletter subscription from {$source} on {$page} page",
                        true
                    ]);
                    
                    $response['success'] = true;
                    $response['message'] = 'Thank you for subscribing!';
                    
                    // Log the subscription
                    error_log("Newsletter subscription: $email from $source on $page");
                    
                    // Send welcome email (optional)
                    try {
                        $welcome_subject = 'Welcome to ' . SITE_NAME . ' Newsletter!';
                        $welcome_body = "
                            <html>
                            <head><title>Welcome to our Newsletter</title></head>
                            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                                    <h2 style='color: #E67E22;'>Welcome to " . SITE_NAME . "!</h2>
                                    <p>Thank you for subscribing to our newsletter. You'll receive the latest updates on architecture, design trends, and our projects.</p>
                                    <p>Stay tuned for exciting content!</p>
                                    <p>Best regards,<br>The " . SITE_NAME . " Team</p>
                                </div>
                            </body>
                            </html>
                        ";
                        
                        sendEmail($email, $welcome_subject, $welcome_body);
                    } catch (Exception $e) {
                        // Don't fail the subscription if email fails
                        error_log("Failed to send welcome email: " . $e->getMessage());
                    }
                    
                } else {
                    $response['message'] = 'Sorry, there was an error processing your subscription. Please try again.';
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Newsletter signup error: " . $e->getMessage());
        $response['message'] = 'Sorry, there was an error processing your subscription. Please try again.';
    }
}

// Handle response based on request type
if (isAjaxRequest()) {
    // AJAX request - return JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
} else {
    // Regular form submission - redirect back with message
    $redirect_url = $_SERVER['HTTP_REFERER'] ?? siteUrl();
    $message = urlencode($response['message']);
    $status = $response['success'] ? 'success' : 'error';
    
    // Add query parameters for message display
    $separator = strpos($redirect_url, '?') !== false ? '&' : '?';
    $redirect_url .= $separator . "newsletter_status=$status&newsletter_message=$message";
    
    header("Location: $redirect_url");
    exit;
}

/**
 * Helper function to check if request is AJAX
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}


?>
