<?php
/**
 * Thank You Page - Contact Form Success
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get message type and content from URL parameters
$message_type = isset($_GET['type']) ? $_GET['type'] : 'contact';
$custom_message = isset($_GET['message']) ? urldecode($_GET['message']) : '';

// Set default messages based on type
$messages = [
    'contact' => [
        'title' => 'Thank You!',
        'message' => 'Thank you for your message. We will get back to you soon!',
        'icon' => '✅',
        'color' => '#28a745'
    ],
    'newsletter' => [
        'title' => 'Subscribed!',
        'message' => 'Thank you for subscribing to our newsletter!',
        'icon' => '📧',
        'color' => '#007cba'
    ],
    'error' => [
        'title' => 'Oops!',
        'message' => 'There was an error processing your request. Please try again.',
        'icon' => '❌',
        'color' => '#dc3545'
    ]
];

$current_message = $messages[$message_type] ?? $messages['contact'];
if ($custom_message) {
    $current_message['message'] = $custom_message;
}
?>
<!DOCTYPE html>

<?php
/**
 * Thank You Page - Monolith Design - Modern, Consistent Styling
 */
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get message type and content from URL parameters
$message_type = isset($_GET['type']) ? $_GET['type'] : 'contact';
$custom_message = isset($_GET['message']) ? urldecode($_GET['message']) : '';

// Theme color integration
$accent_color = getThemeOption('accent_color', ACCENT_COLOR);
$primary_color = getThemeOption('primary_color', PRIMARY_COLOR);
$secondary_color = getThemeOption('secondary_color', SECONDARY_COLOR);
$text_color = getThemeOption('text_color', TEXT_COLOR);

// Set default messages based on type
$messages = [
    'contact' => [
        'title' => 'Thank You!',
        'message' => 'Thank you for your message. We will get back to you soon!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="' . $accent_color . '"/><path d="M14 24l6 6 12-12" stroke="' . $accent_color . '" stroke-width="3" fill="none"/></svg>',
        'color' => $accent_color
    ],
    'newsletter' => [
        'title' => 'Subscribed!',
        'message' => 'Thank you for subscribing to our newsletter!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><rect x="6" y="12" width="36" height="24" rx="4" stroke="' . $accent_color . '"/><polyline points="6,12 24,30 42,12" stroke="' . $accent_color . '" stroke-width="3" fill="none"/></svg>',
        'color' => $accent_color
    ],
    'error' => [
        'title' => 'Oops!',
        'message' => 'Something went wrong. Please try again.',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="#dc3545"/><line x1="16" y1="16" x2="32" y2="32" stroke="#dc3545" stroke-width="3"/><line x1="32" y1="16" x2="16" y2="32" stroke="#dc3545" stroke-width="3"/></svg>',
        'color' => '#dc3545'
    ]
];

$current_message = $messages[$message_type] ?? $messages['contact'];
if ($custom_message) {
    $current_message['message'] = $custom_message;
}

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $current_message['title']; ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="Thank you for contacting <?php echo SITE_NAME; ?>. We will get back to you soon.">
    <meta name="robots" content="noindex, nofollow">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Main CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">
    <style>
        :root {
            --accent-color: <?php echo $accent_color; ?>;
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --text-color: <?php echo $text_color; ?>;
        }
        body {
            background: var(--secondary-color);
            color: var(--text-color);
            font-family: 'Public Sans', Arial, sans-serif;
            min-height: 100vh;
        }
        .thank-you-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            padding: 3rem 1rem;
        }
        .thank-you-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            padding: 2.5rem 2rem;
            max-width: 480px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
        }
        .thank-you-icon {
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: center;
        }
        .thank-you-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }
        .thank-you-message {
            font-size: 1.15rem;
            color: var(--text-color);
            margin-bottom: 2rem;
        }
        .thank-you-actions {
            margin-top: 2rem;
        }
        .primary-button {
            background: var(--accent-color);
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            cursor: pointer;
            transition: background 0.2s;
        }
        .primary-button:hover {
            background: #222;
            color: #fff;
        }
        @media (max-width: 600px) {
            .thank-you-card {
                padding: 1.5rem 0.5rem;
            }
            .thank-you-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <?php loadTemplate('header'); ?>
    <div class="thank-you-container">
        <div class="thank-you-card">
            <div class="thank-you-icon"><?php echo $current_message['icon']; ?></div>
            <h1 class="thank-you-title"><?php echo htmlspecialchars($current_message['title']); ?></h1>
            <p class="thank-you-message"><?php echo htmlspecialchars($current_message['message']); ?></p>
            <div class="thank-you-actions">
                <a href="<?php echo siteUrl(); ?>" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                    </svg>
                    Back to Home
                </a>
                <?php if ($message_type === 'contact'): ?>
                <a href="<?php echo siteUrl('projects'); ?>" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zM2.5 2a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zM1 10.5A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3z"/>
                    </svg>
                    View Our Work
                </a>
                <?php endif; ?>
            </div>
            <?php if ($message_type === 'contact'): ?>
            <div class="contact-info">
                <h3>Need immediate assistance?</h3>
                <div class="contact-details">
                    <div class="contact-detail">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122L9.98 10.94a6.678 6.678 0 0 1-3.21-.918 6.678 6.678 0 0 1-.918-3.21l.508-1.804a.678.678 0 0 0-.122-.58L3.654 1.328z"/>
                        </svg>
                        +1 (555) 123-4567
                    </div>
                    <div class="contact-detail">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M2 2A2 2 0 0 0 0 4v.793c.**************.33.084L7.5 8.5a1 1 0 0 0 1 0l7.17-3.623c.11-.026.224-.052.33-.084V4a2 2 0 0 0-2-2H2zm3.5 6.033L0 6.766V14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6.766L10.5 8.033a3 3 0 0 1-3 0z"/>
                        </svg>
                        <EMAIL>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php loadTemplate('footer'); ?>
</body>
</html>
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: <?php echo $current_message['color']; ?>;
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .contact-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        
        .contact-info h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .contact-details {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #666;
        }
        
        .contact-detail {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @media (max-width: 768px) {
            .thank-you-card {
                padding: 40px 20px;
            }
            
            .thank-you-title {
                font-size: 2rem;
            }
            
            .thank-you-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-details {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="thank-you-container">
        <div class="thank-you-card">
            <div class="thank-you-icon"><?php echo $current_message['icon']; ?></div>
            
            <h1 class="thank-you-title"><?php echo htmlspecialchars($current_message['title']); ?></h1>
            
            <p class="thank-you-message"><?php echo htmlspecialchars($current_message['message']); ?></p>
            
            <div class="thank-you-actions">
                <a href="<?php echo siteUrl(); ?>" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                    </svg>
                    Back to Home
                </a>
                
                <?php if ($message_type === 'contact'): ?>
                <a href="<?php echo siteUrl('projects'); ?>" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zM2.5 2a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zM1 10.5A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3z"/>
                    </svg>
                    View Our Work
                </a>
                <?php endif; ?>
            </div>
            
            <?php if ($message_type === 'contact'): ?>
            <div class="contact-info">
                <h3>Need immediate assistance?</h3>
                <div class="contact-details">
                    <div class="contact-detail">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122L9.98 10.94a6.678 6.678 0 0 1-3.21-.918 6.678 6.678 0 0 1-.918-3.21l.508-1.804a.678.678 0 0 0-.122-.58L3.654 1.328z"/>
                        </svg>
                        +1 (555) 123-4567
                    </div>
                    <div class="contact-detail">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M2 2A2 2 0 0 0 0 4v.793c.**************.33.084L7.5 8.5a1 1 0 0 0 1 0l7.17-3.623c.11-.026.224-.052.33-.084V4a2 2 0 0 0-2-2H2zm3.5 6.033L0 6.766V14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6.766L10.5 8.033a3 3 0 0 1-3 0z"/>
                        </svg>
                        <EMAIL>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Auto-redirect after 10 seconds (optional)
        setTimeout(function() {
            // Uncomment the line below if you want auto-redirect
            // window.location.href = '<?php echo siteUrl(); ?>';
        }, 10000);
        
        // Track the thank you page visit
        console.log('Thank you page loaded:', {
            type: '<?php echo $message_type; ?>',
            message: '<?php echo addslashes($current_message['message']); ?>',
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>
