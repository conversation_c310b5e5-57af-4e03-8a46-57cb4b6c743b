<?php
/**
 * Thank You Page - Monolith Design
 * Uses proper header/footer includes for consistency
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get message type and content from URL parameters
$message_type = isset($_GET['type']) ? $_GET['type'] : 'contact';
$custom_message = isset($_GET['message']) ? urldecode($_GET['message']) : '';

// Set default messages based on type
$messages = [
    'contact' => [
        'title' => 'Thank You!',
        'message' => 'Thank you for your message. We will get back to you soon!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="' . ACCENT_COLOR . '"/><path d="M14 24l6 6 12-12" stroke="' . ACCENT_COLOR . '" stroke-width="3" fill="none"/></svg>',
        'color' => ACCENT_COLOR
    ],
    'newsletter' => [
        'title' => 'Subscribed!',
        'message' => 'Thank you for subscribing to our newsletter!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><rect x="6" y="12" width="36" height="24" rx="4" stroke="' . ACCENT_COLOR . '"/><polyline points="6,12 24,30 42,12" stroke="' . ACCENT_COLOR . '" stroke-width="3" fill="none"/></svg>',
        'color' => ACCENT_COLOR
    ],
    'error' => [
        'title' => 'Oops!',
        'message' => 'Something went wrong. Please try again.',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="#dc3545"/><line x1="16" y1="16" x2="32" y2="32" stroke="#dc3545" stroke-width="3"/><line x1="32" y1="16" x2="16" y2="32" stroke="#dc3545" stroke-width="3"/></svg>',
        'color' => '#dc3545'
    ]
];

$current_message = $messages[$message_type] ?? $messages['contact'];
if ($custom_message) {
    $current_message['message'] = $custom_message;
}

// Set page title for header
$page_title = $current_message['title'] . ' - ' . SITE_NAME;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Thank you for contacting <?php echo SITE_NAME; ?>. We will get back to you soon.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <style>
        /* Thank You Page Specific Styles */
        .thank-you-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 70vh;
            padding: 120px 20px 100px 20px; /* 120px top padding, 100px bottom padding */
            background: var(--secondary-color, #F5F5F5);
        }

        .thank-you-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            padding: 100px 60px; /* 100px padding around card content */
            max-width: 600px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            position: relative;
        }
        .thank-you-icon {
            margin-bottom: 2rem;
            display: flex;
            justify-content: center;
        }

        .thank-you-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: <?php echo ACCENT_COLOR; ?>;
            margin-bottom: 1.5rem;
        }

        .thank-you-message {
            font-size: 1.2rem;
            color: <?php echo TEXT_COLOR; ?>;
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .thank-you-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .primary-button {
            background: <?php echo ACCENT_COLOR; ?>;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .primary-button:hover {
            background: #d35400;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(230, 126, 34, 0.3);
        }

        .secondary-button {
            background: transparent;
            color: <?php echo ACCENT_COLOR; ?>;
            border: 2px solid <?php echo ACCENT_COLOR; ?>;
            border-radius: 8px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .secondary-button:hover {
            background: <?php echo ACCENT_COLOR; ?>;
            color: #ffffff;
        }

        /* Contact Info Section */
        .contact-info {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #E5E5E5;
        }

        .contact-info h3 {
            font-size: 1.5rem;
            color: <?php echo PRIMARY_COLOR; ?>;
            margin-bottom: 1rem;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            align-items: center;
        }

        .contact-details a {
            color: <?php echo ACCENT_COLOR; ?>;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-details a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .thank-you-section {
                padding: 80px 15px 50px 15px; /* 80px top padding on mobile */
            }

            .thank-you-card {
                padding: 50px 30px;
            }

            .thank-you-title {
                font-size: 2rem;
            }

            .thank-you-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <section class="thank-you-section">
            <div class="thank-you-card">
                <div class="thank-you-icon"><?php echo $current_message['icon']; ?></div>
                <h1 class="thank-you-title"><?php echo htmlspecialchars($current_message['title']); ?></h1>
                <p class="thank-you-message"><?php echo htmlspecialchars($current_message['message']); ?></p>

                <div class="thank-you-actions">
                    <a href="<?php echo siteUrl(); ?>" class="primary-button">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                        </svg>
                        Back to Home
                    </a>
                    <?php if ($message_type === 'contact'): ?>
                    <a href="<?php echo siteUrl('work'); ?>" class="secondary-button">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zM2.5 2a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zM1 10.5A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3z"/>
                        </svg>
                        View Our Work
                    </a>
                    <?php endif; ?>
                </div>
                <?php if ($message_type === 'contact'): ?>
                <div class="contact-info">
                    <h3>Need immediate assistance?</h3>
                    <div class="contact-details">
                        <a href="tel:+15551234567" class="contact-detail">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122L9.98 10.94a6.678 6.678 0 0 1-3.21-.918 6.678 6.678 0 0 1-.918-3.21l.508-1.804a.678.678 0 0 0-.122-.58L3.654 1.328z"/>
                            </svg>
                            +****************
                        </a>
                        <a href="mailto:<EMAIL>" class="contact-detail">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M2 2A2 2 0 0 0 0 4v.793c.106.032.22.058.33.084L7.5 8.5a1 1 0 0 0 1 0l7.17-3.623c.11-.026.224-.052.33-.084V4a2 2 0 0 0-2-2H2zm3.5 6.033L0 6.766V14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6.766L10.5 8.033a3 3 0 0 1-3 0z"/>
                            </svg>
                            <EMAIL>
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>
</body>
</html>
