<?php
/**
 * Admin Login Page - Beautiful Interactive Design
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// If already logged in, redirect to dashboard
if (isset($_SESSION['admin_logged_in'])) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

// Get login page customization settings
$login_settings = [
    'background_type' => getThemeOption('login_background_type', 'gradient'),
    'background_color' => getThemeOption('login_background_color', '#1A1A1A'),
    'gradient_start' => getThemeOption('login_background_gradient_start', '#1A1A1A'),
    'gradient_end' => getThemeOption('login_background_gradient_end', '#E67E22'),
    'gradient_direction' => getThemeOption('login_background_gradient_direction', '45deg'),
    'background_image' => getThemeOption('login_background_image', ''),
    'background_video' => getThemeOption('login_background_video', ''),
    'overlay_opacity' => getThemeOption('login_overlay_opacity', '0.7'),
    'overlay_color' => getThemeOption('login_overlay_color', '#000000'),
    'particles_enabled' => getThemeOption('login_particles_enabled', '1'),
    'particles_url' => getThemeOption('login_particles_url', ''),
    'animation_enabled' => getThemeOption('login_animation_enabled', '1')
];

if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Database authentication with role-based access
    try {
        $db = Database::getConnection();

        // Log login attempt (if table exists)
        $stmt = $db->query("SHOW TABLES LIKE 'admin_login_attempts'");
        if ($stmt->rowCount() > 0) {
            $stmt = $db->prepare("INSERT INTO admin_login_attempts (username, ip_address, user_agent, attempted_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$username, $ip_address, $user_agent]);
        }

        // Check for user in database
        $stmt = $db->prepare("SELECT id, username, password, email, role, status, last_login FROM admin_users WHERE username = ? AND status = 'active'");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Update login attempt as successful (if table exists)
            $stmt = $db->query("SHOW TABLES LIKE 'admin_login_attempts'");
            if ($stmt->rowCount() > 0) {
                $stmt = $db->prepare("UPDATE admin_login_attempts SET success = TRUE WHERE username = ? AND ip_address = ? ORDER BY attempted_at DESC LIMIT 1");
                $stmt->execute([$username, $ip_address]);
            }

            // Update last login time
            $stmt = $db->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);

            // Create secure session
            session_regenerate_id(true);
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_user_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_email'] = $user['email'];
            $_SESSION['admin_role'] = $user['role'];
            $_SESSION['admin_login_time'] = time();
            $_SESSION['admin_ip'] = $ip_address;

            // Create session record (if table exists)
            $stmt = $db->query("SHOW TABLES LIKE 'admin_sessions'");
            if ($stmt->rowCount() > 0) {
                $session_id = session_id();
                $expires_at = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours
                $stmt = $db->prepare("INSERT INTO admin_sessions (user_id, session_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$user['id'], $session_id, $ip_address, $user_agent, $expires_at]);
            }

            $success = 'Login successful! Redirecting...';

            // Redirect based on role
            if ($user['role'] === 'contact_admin') {
                header('refresh:2;url=contacts.php');
            } else {
                header('refresh:2;url=index.php');
            }
        } else {
            $error = 'Invalid username or password';
        }
    } catch (Exception $e) {
        $error = 'Login system error. Please try again.';
        error_log("Admin login error: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Monolith Design Co.</title>

    <!-- Favicon (matches admin dashboard) -->
    <link rel="icon" type="image/x-icon" href="<?php echo getThemeOption('favicon', themeUrl('images/favicon.ico')); ?>">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo PRIMARY_COLOR; ?>; /* Monolith brand primary */
            --accent-color: <?php echo ACCENT_COLOR; ?>; /* Monolith brand accent */
            --secondary-color: #2c3e50; /* Darker complement */
            --success-color: #27ae60; /* Success green */
            --error-color: #e74c3c; /* Error red */
            --text-light: #e0e0e0;
            --text-white: #ffffff;
            --background-dark: rgba(0, 0, 0, 0.7);
            --form-background: rgba(255, 255, 255, 0.05);
            --border-color: rgba(255, 255, 255, 0.2);
            --glass-background: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Poppins', sans-serif;
            color: var(--text-light);
            overflow: hidden;
        }

        /* Dynamic Background */
        .dynamic-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            <?php
            switch($login_settings['background_type']) {
                case 'solid':
                    echo 'background: ' . $login_settings['background_color'] . ';';
                    break;
                case 'gradient':
                    $direction = $login_settings['gradient_direction'];
                    if ($direction === 'radial') {
                        echo 'background: radial-gradient(circle, ' . $login_settings['gradient_start'] . ', ' . $login_settings['gradient_end'] . ');';
                    } else {
                        echo 'background: linear-gradient(' . $direction . ', ' . $login_settings['gradient_start'] . ', var(--secondary-color), ' . $login_settings['gradient_end'] . ');';
                    }
                    if ($login_settings['animation_enabled'] === '1') {
                        echo 'background-size: 400% 400%; animation: gradientShift 15s ease infinite;';
                    }
                    break;
                case 'image':
                    if (!empty($login_settings['background_image'])) {
                        echo 'background: url("' . $login_settings['background_image'] . '") center/cover no-repeat;';
                    } else {
                        echo 'background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));';
                    }
                    break;
                case 'video':
                    echo 'background: var(--primary-color);'; // Fallback for video
                    break;
                default:
                    echo 'background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));';
                    if ($login_settings['animation_enabled'] === '1') {
                        echo 'background-size: 400% 400%; animation: gradientShift 15s ease infinite;';
                    }
            }
            ?>
        }

        <?php if ($login_settings['animation_enabled'] === '1'): ?>
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        <?php endif; ?>

        /* Floating particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .background-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            <?php
            // Convert hex to rgba
            $overlay_color = $login_settings['overlay_color'];
            $opacity = $login_settings['overlay_opacity'];
            if (strlen($overlay_color) === 7) {
                $r = hexdec(substr($overlay_color, 1, 2));
                $g = hexdec(substr($overlay_color, 3, 2));
                $b = hexdec(substr($overlay_color, 5, 2));
                echo "background: rgba($r, $g, $b, $opacity);";
            } else {
                echo "background: rgba(0, 0, 0, $opacity);";
            }
            ?>
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-box {
            background: var(--form-background);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            text-align: center;
            max-width: 450px;
            width: 100%;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .login-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .login-box:hover::before {
            left: 100%;
        }

        .login-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px 0 rgba(0, 0, 0, 0.5);
        }

        .logo-section {
            margin-bottom: 40px;
            position: relative;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: var(--glass-background);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
            padding: 15px;
        }

        .logo-icon:hover {
            transform: rotate(360deg);
            background: var(--accent-color);
            border-color: var(--accent-color);
        }

        .logo-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            /* Remove filter for SVG favicon - it has its own colors */
            transition: all 0.3s ease;
        }

        .logo-icon:hover img {
            /* Keep original colors on hover for better visibility */
            transform: scale(1.1);
        }

        /* Fallback for Font Awesome icon if image fails to load */
        .logo-icon i {
            font-size: 2.5rem;
            color: var(--text-white);
            display: none; /* Hidden by default, shown only if image fails */
        }

        .login-box h2 {
            margin-bottom: 10px;
            color: var(--text-white);
            font-size: 2.5em;
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        /* Alert Messages */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease-out;
        }

        .alert-error {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
            color: #ff6b6b;
        }

        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            border: 1px solid rgba(39, 174, 96, 0.3);
            color: #2ecc71;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Form Styling */
        .input-group {
            position: relative;
            margin-bottom: 30px;
        }

        .input-group input {
            width: 100%;
            padding: 18px 15px;
            background: var(--glass-background);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            outline: none;
            color: var(--text-white);
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-group input::placeholder {
            color: transparent;
        }

        .input-group label {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            pointer-events: none;
            transition: all 0.3s ease;
            background: transparent;
            padding: 0 5px;
        }

        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label,
        .input-group input:valid + label {
            top: -8px;
            left: 10px;
            font-size: 0.85rem;
            color: var(--accent-color);
            background: rgba(0, 0, 0, 0.8);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .input-group input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 20px rgba(230, 126, 34, 0.3);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .input-group input:focus ~ .input-icon {
            color: var(--accent-color);
        }

        /* Login Button */
        #login-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, var(--accent-color), #d35400);
            color: var(--text-white);
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Poppins', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
            overflow: hidden;
            position: relative;
            text-transform: uppercase;
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
        }

        #login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        #login-button:hover::before {
            left: 100%;
        }

        #login-button:hover {
            background: linear-gradient(135deg, #d35400, var(--accent-color));
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(230, 126, 34, 0.4);
        }

        #login-button:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
        }

        #login-button.loading {
            background: var(--secondary-color);
            cursor: not-allowed;
            transform: none;
        }

        #login-button .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid var(--text-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        #login-button.loading .spinner {
            display: block;
        }

        #login-button.loading .button-text {
            visibility: hidden;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Footer Link */
        .footer-link {
            margin-top: 25px;
            font-size: 0.9rem;
        }

        .footer-link a {
            color: var(--accent-color);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .footer-link a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--accent-color);
            transition: width 0.3s ease;
        }

        .footer-link a:hover::after {
            width: 100%;
        }

        .footer-link a:hover {
            color: #f39c12;
            text-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-box {
                padding: 40px 30px;
                margin: 20px;
                border-radius: 15px;
            }

            .login-box h2 {
                font-size: 2rem;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
                padding: 12px;
            }

            .logo-icon i {
                font-size: 2rem;
            }

            .input-group input {
                padding: 15px 12px;
                font-size: 0.95rem;
            }

            #login-button {
                padding: 15px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }

            .login-box {
                padding: 30px 20px;
                border-radius: 12px;
            }

            .login-box h2 {
                font-size: 1.8rem;
                margin-bottom: 8px;
            }

            .login-subtitle {
                font-size: 0.9rem;
                margin-bottom: 25px;
            }

            .input-group {
                margin-bottom: 25px;
            }

            .logo-icon {
                width: 50px;
                height: 50px;
                margin-bottom: 15px;
                padding: 10px;
            }

            .logo-icon i {
                font-size: 1.5rem;
            }
        }

        /* Video Background Support */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -3;
            display: <?php echo ($login_settings['background_type'] === 'video' && !empty($login_settings['background_video'])) ? 'block' : 'none'; ?>;
        }

        .video-background video {
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Video Background -->
    <?php if ($login_settings['background_type'] === 'video' && !empty($login_settings['background_video'])): ?>
    <div class="video-background">
        <video autoplay muted loop playsinline id="bg-video">
            <source src="<?php echo $login_settings['background_video']; ?>" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
    <?php endif; ?>

    <!-- Dynamic Background -->
    <div class="dynamic-background"></div>

    <!-- Floating Particles -->
    <?php if ($login_settings['particles_enabled'] === '1'): ?>
    <div class="particles" id="particles"></div>
    <?php endif; ?>

    <!-- Background Overlay -->
    <div class="background-overlay"></div>

    <div class="login-container">
        <div class="login-box">
            <div class="logo-section">
                <div class="logo-icon">
                    <img src="<?php echo getThemeOption('favicon', themeUrl('images/favicon.ico')); ?>"
                         alt="Monolith Design Co."
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <i class="fas fa-building"></i>
                </div>
                <h2>MONOLITH</h2>
                <p class="login-subtitle">Admin Dashboard</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <form method="POST" id="login-form">
                <div class="input-group">
                    <input type="text" id="username" name="username" required placeholder=" ">
                    <label for="username">Username or Email</label>
                    <i class="fas fa-user input-icon"></i>
                </div>

                <div class="input-group">
                    <input type="password" id="password" name="password" required placeholder=" ">
                    <label for="password">Password</label>
                    <i class="fas fa-lock input-icon"></i>
                </div>

                <button type="submit" id="login-button">
                    <span class="spinner"></span>
                    <span class="button-text">Access Dashboard</span>
                </button>
            </form>

            <div class="footer-link">
                <a href="../" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Website
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize particles if enabled
            <?php if ($login_settings['particles_enabled'] === '1'): ?>
                <?php if (!empty($login_settings['particles_url'])): ?>
                    // Load custom particles from URL
                    loadCustomParticles('<?php echo $login_settings['particles_url']; ?>');
                <?php else: ?>
                    // Use default CSS particles
                    createParticles();
                <?php endif; ?>
            <?php endif; ?>

            // Form handling
            const loginForm = document.getElementById('login-form');
            const loginButton = document.getElementById('login-button');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // Enhanced form submission with loading state
            loginForm.addEventListener('submit', (event) => {
                if (loginButton.classList.contains('loading')) {
                    event.preventDefault();
                    return;
                }

                // Add loading state
                loginButton.classList.add('loading');

                // If there's a success message, don't prevent default (let PHP handle redirect)
                <?php if ($success): ?>
                    return; // Let the form submit normally for redirect
                <?php endif; ?>
            });

            // Input focus effects
            document.querySelectorAll('.input-group input').forEach(input => {
                input.addEventListener('focus', () => {
                    input.parentElement.classList.add('is-focused');
                });

                input.addEventListener('blur', () => {
                    input.parentElement.classList.remove('is-focused');
                });

                // Real-time validation feedback
                input.addEventListener('input', () => {
                    if (input.value.length > 0) {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // Enter key to submit form
                if (e.key === 'Enter' && !loginButton.classList.contains('loading')) {
                    if (document.activeElement === usernameInput) {
                        passwordInput.focus();
                    } else if (document.activeElement === passwordInput) {
                        loginForm.submit();
                    }
                }

                // Escape key to clear form
                if (e.key === 'Escape') {
                    usernameInput.value = '';
                    passwordInput.value = '';
                    usernameInput.focus();
                }
            });

            // Auto-focus username field
            usernameInput.focus();

            // Load custom particles from URL
            function loadCustomParticles(url) {
                const particlesContainer = document.getElementById('particles');
                if (!particlesContainer) return;

                // Check if it's a particles.js JSON config
                if (url.includes('.json') || url.includes('particles')) {
                    // Load particles.js library and config
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js';
                    script.onload = () => {
                        fetch(url)
                            .then(response => response.json())
                            .then(config => {
                                particlesJS('particles', config);
                            })
                            .catch(error => {
                                console.warn('Failed to load custom particles config:', error);
                                createParticles(); // Fallback to default
                            });
                    };
                    script.onerror = () => {
                        console.warn('Failed to load particles.js library');
                        createParticles(); // Fallback to default
                    };
                    document.head.appendChild(script);
                } else {
                    // Assume it's a direct script URL
                    const script = document.createElement('script');
                    script.src = url;
                    script.onload = () => {
                        console.log('Custom particles script loaded');
                    };
                    script.onerror = () => {
                        console.warn('Failed to load custom particles script');
                        createParticles(); // Fallback to default
                    };
                    document.head.appendChild(script);
                }
            }

            // Create floating particles (default)
            function createParticles() {
                const particlesContainer = document.getElementById('particles');
                if (!particlesContainer) return; // Exit if particles are disabled

                const particleCount = 50;

                for (let i = 0; i < particleCount; i++) {
                    createParticle(particlesContainer);
                }
            }

            function createParticle(container) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 8px
                const size = Math.random() * 6 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random horizontal position
                particle.style.left = Math.random() * 100 + '%';

                // Random animation duration between 15s and 25s
                const duration = Math.random() * 10 + 15;
                particle.style.animationDuration = duration + 's';

                // Random delay
                const delay = Math.random() * 20;
                particle.style.animationDelay = delay + 's';

                container.appendChild(particle);

                // Remove and recreate particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.remove();
                        createParticle(container);
                    }
                }, (duration + delay) * 1000);
            }

            // Video background support (for future use)
            const bgVideo = document.getElementById('bg-video');
            if (bgVideo) {
                bgVideo.play().catch(error => {
                    console.warn("Video autoplay failed:", error);
                    // Fallback: hide video background and show animated background
                    document.querySelector('.video-background').style.display = 'none';
                });
            }

            // Add subtle mouse movement effect
            document.addEventListener('mousemove', (e) => {
                const loginBox = document.querySelector('.login-box');
                const rect = loginBox.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;

                const rotateX = (y / rect.height) * 5;
                const rotateY = (x / rect.width) * -5;

                loginBox.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-5px)`;
            });

            // Reset transform when mouse leaves
            document.addEventListener('mouseleave', () => {
                const loginBox = document.querySelector('.login-box');
                loginBox.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(0px)';
            });

            // Success message auto-redirect
            <?php if ($success): ?>
                setTimeout(() => {
                    window.location.href = 'index.php';
                }, 2000);
            <?php endif; ?>
        });
    </script>
</body>
</html>
