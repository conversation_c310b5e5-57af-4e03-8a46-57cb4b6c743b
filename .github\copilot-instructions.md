# Copilot Instructions for Monolith Design Co. Codebase

## Overview
Professional PHP website theme for engineering and architecture firms. Modular, secure, and database-driven, with a comprehensive admin dashboard. Designed for easy customization and robust security. See `test_delete/README.md` for product scope and features.

## Architecture & Key Components
- **Root Pages**: Main site pages (`index.php`, `about.php`, `services.php`, etc.)
- **Admin Panel**: `/admin/` manages all content (services, projects, team, blog, site options).
- **Templates**: Shared UI in `/templates/` (header, footer, hero-cta, etc.).
- **Components**: Modular sections (e.g., `components/footer/` with dedicated loader, CSS, and sections; see its README for structure).
- **Assets**: Static files in `/assets/` (CSS, JS, images). Images follow strict requirements—see `/assets/images/README.md`.
- **Includes**: Helper scripts in `/includes/` (functions, advanced slider helpers, hero detection).
- **Config**: Central config in `config.php` (site settings, DB, security, theme options).

## Developer Workflows
- **Setup**:
  1. Upload files to server
  2. Create MySQL database
  3. Visit `install.php` to initialize DB
  4. Delete `install.php` after setup
  5. Access admin at `/admin/`
- **Theme Customization**: Update via admin or directly in `config.php` and `/templates/`.
- **Database**: Structure in `test_delete/database.sql`. All content managed via MySQL.
- **File Uploads**: Images go to `/assets/images/uploads/` (auto-created if missing). See `/assets/images/README.md` for required formats and sizes.
- **Footer**: Use `<?php loadFooter(); ?>` for modular footer loading. Edit sections in `components/footer/sections/`.
- **Security**: CSRF tokens, session hardening, and security headers in `config.php`. All DB queries use prepared statements.
- **Error Reporting**: Controlled by environment detection in `config.php` (production disables display, logs to `error.log`).

## Project-Specific Conventions
- **Clean URLs**: SEO-friendly, routed via `.htaccess` (no `.php` extensions in public URLs).
- **Theme Options**: Defaults in `config.php`, overridden by DB via admin.
- **Responsive Design**: CSS in `/assets/css/` uses 8-point grid, mobile-first.
- **Typography**: Montserrat and Lato via Google Fonts.
- **Footer**: No border radius, modular sections, dedicated CSS (see `components/footer/README.md`).
- **Social & Newsletter**: Managed in theme options and templates.

## Integration Points
- **External Services**: SMTP email (configurable in `config.php`), Google Maps (contact page), Google Fonts.
- **Admin Panel**: All site content, team, projects, and services managed via `/admin/` scripts.

## Examples
- To add a new service: Use `/admin/services.php` or update DB directly.
- To change site logo: Update via admin or replace file in `/assets/images/`.
- To customize footer: Edit `components/footer/sections/` or `/templates/footer.php` and theme options in admin.
- To update hero slider: Use `/admin/sliders.php` and reference `/includes/advanced-slider-helpers.php` for logic.

## References
- For setup: `INSTALLATION.txt`, `test_delete/README.md`, `test_delete/documentation.html`
- For DB schema: `test_delete/database.sql`
- For changelog: `test_delete/Changelog.txt`
- For image requirements: `/assets/images/README.md`
- For footer architecture: `components/footer/README.md`

---
**Agents must follow existing patterns for security, modularity, and maintainability. When in doubt, reference the admin panel, config files, and component READMEs for conventions.**
