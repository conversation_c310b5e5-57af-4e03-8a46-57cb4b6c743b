<?php
/**
 * Admin Header Template
 * Header component for admin dashboard
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Include authentication functions if not already loaded
if (!function_exists('getCurrentAdminUser')) {
    require_once dirname(dirname(dirname(__FILE__))) . '/includes/admin-auth.php';
}

// Get current user info with fallback
$current_user = function_exists('getCurrentAdminUser') ? getCurrentAdminUser() : null;
$user_role_display = function_exists('getRoleDisplayName') ? getRoleDisplayName($current_user['role'] ?? '') : 'Admin';
?>

<header class="admin-header">
    <div class="admin-header-container">
        
        <!-- Logo/Brand Section -->
        <div class="admin-brand">
            <?php
            $headerDisplay = getThemeOption('admin_header_display', 'both');
            if ($headerDisplay === 'logo_only' || $headerDisplay === 'both'):
            ?>
            <div class="brand-logo">
                <img src="<?php echo getThemeOption('site_logo_white', themeUrl('images/logo-white.svg')); ?>" alt="Monolith Design Co." class="logo-image">
            </div>
            <?php endif; ?>

            <?php if ($headerDisplay === 'title_only' || $headerDisplay === 'both'): ?>
            <div class="brand-text">
                <h1 class="brand-title">MONOLITH DESIGN CO.</h1>
                <span class="brand-subtitle">Admin Dashboard</span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Header Actions -->
        <div class="admin-header-actions">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="<?php echo siteUrl(); ?>" 
                   target="_blank" 
                   class="btn btn-outline-light btn-sm"
                   title="View Website">
                    <i class="fas fa-external-link-alt me-1"></i>
                    <span class="d-none d-md-inline">View Website</span>
                </a>
            </div>
            
            <!-- User Menu -->
            <div class="user-menu dropdown">
                <button class="btn btn-link dropdown-toggle user-menu-toggle" 
                        type="button" 
                        id="userMenuDropdown" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <span class="user-name d-none d-md-inline"><?php echo htmlspecialchars($current_user['username'] ?? 'Admin'); ?></span>
                </button>
                
                <ul class="dropdown-menu dropdown-menu-end user-dropdown" aria-labelledby="userMenuDropdown">
                    <li class="dropdown-header">
                        <div class="user-info">
                            <div class="user-avatar-large">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="user-details">
                                <div class="user-name"><?php echo htmlspecialchars($current_user['username'] ?? 'Administrator'); ?></div>
                                <div class="user-email"><?php echo htmlspecialchars($current_user['email'] ?? '<EMAIL>'); ?></div>
                                <div class="user-role">
                                    <small class="text-muted"><?php echo htmlspecialchars($user_role_display); ?></small>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li><hr class="dropdown-divider"></li>

                    <?php if (function_exists('isSuperAdmin') && isSuperAdmin()): ?>
                    <li>
                        <a class="dropdown-item" href="index.php">
                            <i class="fas fa-cog me-2"></i>
                            Theme Settings
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="email-settings.php">
                            <i class="fas fa-envelope me-2"></i>
                            Email Settings
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="users.php">
                            <i class="fas fa-users-cog me-2"></i>
                            User Management
                        </a>
                    </li>
                    <?php elseif (function_exists('isContactAdmin') && isContactAdmin()): ?>
                    <li>
                        <a class="dropdown-item" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>
                            Contact Management
                        </a>
                    </li>
                    <?php endif; ?>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>
            
        </div>
        
    </div>
</header>
