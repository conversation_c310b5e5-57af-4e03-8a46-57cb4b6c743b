<?php
/**
 * Contact Page - Root Level
 * This file serves the contact page content at the clean URL /contact
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Handle contact form submission
$form_message = '';
$form_status = '';
$debug_info = '';

if ($_POST && isset($_POST['submit_contact'])) {
    $debug_info .= "<!-- DEBUG: Form submitted via POST -->\n";
    $result = handleContactForm($_POST);
    $form_message = $result['message'];
    $form_status = $result['success'] ? 'success' : 'error';
    $debug_info .= "<!-- DEBUG: Result - Success: " . ($result['success'] ? 'YES' : 'NO') . ", Message: {$result['message']} -->\n";
    $debug_info .= "<!-- DEBUG: form_message = '{$form_message}', form_status = '{$form_status}' -->\n";
}

// Get services for dropdown
$services = getServices();

// Page metadata
$page_title = 'Contact Us - ' . SITE_NAME;
$page_description = 'Get in touch with Monolith Design Co. for your next engineering or architectural project. Contact us today for a consultation.';
$page_keywords = 'contact, get in touch, consultation, engineering services, architectural services, project inquiry';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">
    
    <!-- Google Fonts - Arkify uses Public Sans and Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <!-- Custom accent color -->
    <style>
        :root {
            --accent-color: <?php echo getThemeOption('accent_color', '#2D5A27'); ?>;
        }

        /* Contact Page Hero Text Color Fix */
        .page-hero .hero-description {
            color: #ffffff !important;
            font-size: 1.25rem;
            opacity: 0.95;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7), 0 0 15px rgba(0, 0, 0, 0.4);
            line-height: 1.6;
        }

        /* Enhanced Contact Form Message Styles */
        .form-message {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-weight: 500;
            transition: opacity 0.3s ease;
            z-index: 1000;
            position: relative;
        }

        .form-message.success {
            background-color: #d4edda !important;
            color: #155724 !important;
            border: 2px solid #c3e6cb !important;
        }

        .form-message.error {
            background-color: #f8d7da !important;
            color: #721c24 !important;
            border: 2px solid #f5c6cb !important;
        }
    </style>
</head>
<body>
    <?php 
    // Output debug information
    if ($debug_info) {
        echo $debug_info;
    }
    ?>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Hero Header Section -->
    <?php
    // Set page name for hero header lookup
    $hero_page_name = 'contact';
    // Load the new hero header template
    include 'templates/hero-header.php';
    ?>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-wrapper">
                <!-- Contact Information -->
                <div class="contact-info">
                    <div class="contact-info-content">
                        <h2><?php echo getThemeOption('contact_info_title', 'Contact Information'); ?></h2>
                        <p><?php echo getThemeOption('contact_info_description', 'Get in touch with us today to discuss your project requirements.'); ?></p>
                        
                        <div class="contact-details">
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Address</h4>
                                    <p><?php echo nl2br(htmlspecialchars(getThemeOption('address', '123 Design Street\nArchitecture City, AC 12345'))); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Phone</h4>
                                    <p><?php echo htmlspecialchars(getThemeOption('phone_number', '+****************')); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                        <polyline points="22,6 12,13 2,6"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Email</h4>
                                    <p><?php echo htmlspecialchars(getThemeOption('email', '<EMAIL>')); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Business Hours</h4>
                                    <p><?php echo nl2br(htmlspecialchars(getThemeOption('business_hours', 'Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed'))); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="contact-form">
                    <div class="contact-form-content">
                        <h2><?php echo getThemeOption('contact_form_title', 'Send us a Message'); ?></h2>
                        <p><?php echo getThemeOption('contact_form_description', 'Fill out the form below and we\'ll get back to you as soon as possible.'); ?></p>

                        <?php if ($form_message): ?>
                            <!-- Contact Form Success/Error Message -->
                            <div class="form-message <?php echo $form_status; ?>" id="contactFormMessage" style="display: block !important; visibility: visible !important;">
                                <strong><?php echo $form_status === 'success' ? '✅ Success!' : '❌ Error:'; ?></strong>
                                <?php echo htmlspecialchars($form_message); ?>
                            </div>
                            <?php 
                            // Additional debug output in HTML
                            echo "<!-- CONTACT FORM DEBUG: Message='{$form_message}', Status='{$form_status}' -->\n";
                            ?>
                        <?php else: ?>
                            <!-- No message to display -->
                            <?php echo "<!-- CONTACT FORM DEBUG: No form message to display -->\n"; ?>
                        <?php endif; ?>

                        <form method="POST" action="" class="contact-form-inner">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name *</label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="service">Service Interested In</label>
                                    <select id="service" name="service">
                                        <option value="">Select a service...</option>
                                        <?php if (!empty($services)): ?>
                                            <?php foreach ($services as $service): ?>
                                                <option value="<?php echo htmlspecialchars($service['title']); ?>">
                                                    <?php echo htmlspecialchars($service['title']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project..."></textarea>
                            </div>

                            <button type="submit" name="submit_contact" class="submit-btn">
                                Send Message
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section (Optional) -->
    <?php if (getThemeOption('contact_show_map', '1') === '1'): ?>
    <section class="map-section">
        <div class="container">
            <div class="map-wrapper">
                <h2><?php echo getThemeOption('contact_map_title', 'Find Us'); ?></h2>
                <div class="map-container">
                    <?php 
                    $map_embed = getThemeOption('contact_map_embed', '');
                    if (!empty($map_embed)): 
                        echo $map_embed;
                    else: 
                    ?>
                        <div class="map-placeholder">
                            <p>Map integration available in admin panel</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Hero CTA Section -->
    <?php
    loadTemplate('hero-cta', ['hero_page_name' => 'contact']);
    ?>

    <!-- CONTACT FORM POPUP SUCCESS MESSAGE -->
    <?php if ($form_message): ?>
    <div class="contact-popup-overlay" id="contactPopupOverlay"></div>
    <div class="contact-popup-<?php echo $form_status; ?>" id="contactPopupMessage">
        <div class="popup-icon">
            <?php echo $form_status === 'success' ? '✅' : '❌'; ?>
        </div>
        <div class="popup-title">
            <?php echo $form_status === 'success' ? 'Message Sent!' : 'Error'; ?>
        </div>
        <div class="popup-message"><?php echo htmlspecialchars($form_message); ?></div>
        <div class="popup-close-hint">Click anywhere to close</div>
    </div>
    <?php endif; ?>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- Contact Form Popup Styles -->
    <style>
    /* CONTACT FORM POPUP STYLES */
    .contact-popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 10000;
        animation: overlayFadeIn 0.3s ease-out;
        display: block !important; /* Show overlay when form message exists */
    }

    .contact-popup-success, .contact-popup-error {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        padding: 40px 50px;
        border-radius: 20px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        z-index: 10001;
        text-align: center;
        animation: popupSlideIn 0.5s ease-out;
        min-width: 350px;
        max-width: 90vw;
        display: block !important; /* Show popup when form message exists */
    }

    .contact-popup-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .contact-popup-error {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .contact-popup-success .popup-icon,
    .contact-popup-error .popup-icon {
        font-size: 48px;
        margin-bottom: 15px;
        animation: iconBounce 0.8s ease-out;
    }

    .contact-popup-success .popup-title,
    .contact-popup-error .popup-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .contact-popup-success .popup-message,
    .contact-popup-error .popup-message {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
        line-height: 1.4;
    }

    .contact-popup-success .popup-close-hint,
    .contact-popup-error .popup-close-hint {
        font-size: 14px;
        opacity: 0.8;
        font-style: italic;
    }

    @keyframes overlayFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes popupSlideIn {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) translateY(0);
        }
    }

    @keyframes iconBounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    </style>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.contact-form-inner');
            const submitBtn = form.querySelector('.submit-btn');

            // Handle contact form popup
            const contactOverlay = document.getElementById('contactPopupOverlay');
            const contactPopup = document.getElementById('contactPopupMessage');

            if (contactOverlay && contactPopup) {
                console.log('Contact form popup elements found, setting up event handlers');

                function closeContactPopup() {
                    contactOverlay.style.display = 'none';
                    contactPopup.style.display = 'none';
                    console.log('Contact popup closed');
                }

                // Show the popup when page loads with a message
                contactOverlay.style.display = 'block';
                contactPopup.style.display = 'block';
                console.log('Contact popup displayed');

                contactOverlay.addEventListener('click', closeContactPopup);
                contactPopup.addEventListener('click', closeContactPopup);

                // Auto-close after 7 seconds (longer for contact form)
                setTimeout(closeContactPopup, 7000);

                console.log('Contact form popup will auto-close in 7 seconds');
            }
            
            form.addEventListener('submit', function(e) {
                // Show loading state
                submitBtn.innerHTML = `
                    <span>Sending...</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                    </svg>
                `;
                submitBtn.disabled = true;
            });

            // Auto-hide success/error messages after 5 seconds
            const formMessage = document.querySelector('.form-message');
            if (formMessage) {
                setTimeout(() => {
                    formMessage.style.opacity = '0';
                    setTimeout(() => {
                        formMessage.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        });
    </script>
</body>
</html>
