<?php
define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

echo "Checking hero header for architectural-design service:\n";

$hero = getHeroHeader('service-details-architectural-design');
if ($hero) {
    echo "✅ Hero found!\n";
    echo "Title: " . $hero['page_title'] . "\n";
    echo "Subtitle: " . ($hero['subtitle'] ?? 'None') . "\n";
    echo "Background Type: " . $hero['background_type'] . "\n";
    echo "Background Image: " . ($hero['background_image'] ?? 'None') . "\n";
    echo "Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ No hero found\n";
    echo "Running sync...\n";
    syncServiceHeroHeaders();
    
    $hero = getHeroHeader('service-details-architectural-design');
    if ($hero) {
        echo "✅ Hero created after sync!\n";
        echo "Title: " . $hero['page_title'] . "\n";
    } else {
        echo "❌ Still no hero after sync\n";
    }
}
?>
