<?php
/**
 * Contact Submissions Table Content
 * Table content for contact submissions management
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure submissions variable is available
if (!isset($submissions)) {
    $submissions = [];
}
?>

<?php if (empty($submissions)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        No contact submissions found.
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Type</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Message</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($submissions as $index => $submission): ?>
                    <tr class="searchable-item">
                        <td class="text-center">
                            <span class="text-muted"><?php echo $index + 1; ?></span>
                        </td>
                        <td>
                            <?php if (!empty($submission['is_newsletter_signup'])): ?>
                                <span class="badge bg-info">
                                    <i class="fas fa-envelope me-1"></i>Newsletter
                                </span>
                            <?php else: ?>
                                <span class="badge bg-primary">
                                    <i class="fas fa-phone me-1"></i>Contact
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo htmlspecialchars($submission['name']); ?></strong>
                        </td>
                        <td>
                            <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>" 
                               class="text-decoration-none">
                                <?php echo htmlspecialchars($submission['email']); ?>
                            </a>
                        </td>
                        <td>
                            <?php if ($submission['phone']): ?>
                                <a href="tel:<?php echo htmlspecialchars($submission['phone']); ?>" 
                                   class="text-decoration-none">
                                    <?php echo htmlspecialchars($submission['phone']); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">Not provided</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="message-preview" style="max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <?php
                                $message = htmlspecialchars($submission['message']);
                                echo strlen($message) > 60 ? substr($message, 0, 60) . '...' : $message;
                                ?>
                                <?php if (strlen($submission['message']) > 60): ?>
                                    <button type="button"
                                            class="btn btn-link btn-sm p-0 ms-1"
                                            data-bs-toggle="modal"
                                            data-bs-target="#messageModal<?php echo $submission['id']; ?>"
                                            title="View full message">
                                        <i class="fas fa-expand-alt"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('M j, Y g:i A', strtotime($submission['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <?php
                            $status = $submission['status'] ?? 'unread';
                            $statusClass = [
                                'unread' => 'bg-warning',
                                'read' => 'bg-info',
                                'replied' => 'bg-success'
                            ][$status] ?? 'bg-secondary';
                            ?>
                            <span class="badge <?php echo $statusClass; ?>">
                                <?php echo ucfirst($status); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        data-bs-toggle="modal"
                                        data-bs-target="#messageModal<?php echo $submission['id']; ?>"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>

                                <?php if ($status !== 'read'): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="mark_read">
                                        <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-info" 
                                                title="Mark as Read">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <?php if ($status !== 'replied'): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="mark_replied">
                                        <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-success" 
                                                title="Mark as Replied">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                    <button type="submit" 
                                            class="btn btn-sm btn-outline-danger" 
                                            title="Delete Submission"
                                            data-confirm="Are you sure you want to delete this submission?">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php
    $total_items = count($submissions);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php';
    ?>

    <!-- Contact Details Modals -->
    <?php foreach ($submissions as $submission): ?>
            <div class="modal fade" id="messageModal<?php echo $submission['id']; ?>" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                Message from <?php echo htmlspecialchars($submission['name']); ?>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Email:</strong> 
                                    <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>">
                                        <?php echo htmlspecialchars($submission['email']); ?>
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <strong>Phone:</strong> 
                                    <?php if ($submission['phone']): ?>
                                        <a href="tel:<?php echo htmlspecialchars($submission['phone']); ?>">
                                            <?php echo htmlspecialchars($submission['phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        Not provided
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>Service:</strong> 
                                    <?php echo $submission['service'] ? htmlspecialchars($submission['service']) : 'Not specified'; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Date:</strong> 
                                    <?php echo date('M j, Y g:i A', strtotime($submission['created_at'])); ?>
                                </div>
                            </div>
                            <hr>
                            <div class="message-content">
                                <strong>Message:</strong>
                                <div class="mt-2 p-3 bg-light rounded">
                                    <?php echo nl2br(htmlspecialchars($submission['message'])); ?>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>?subject=Re: Your inquiry" 
                               class="btn btn-primary">
                                <i class="fas fa-reply me-1"></i>
                                Reply via Email
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
    <?php endforeach; ?>
    
<?php endif; ?>
