<?php
/**
 * Theme Settings Content Template
 * Organized settings with tabs for different categories
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure settings variable is available
if (!isset($settings)) {
    $settings = [];
}
?>

<div class="admin-tabs">
    <!-- Tab Navigation -->
    <div class="tabs">
        <button class="tab active" onclick="showTab('general-settings')">
            <i class="fas fa-cog me-2"></i>
            General Settings
        </button>
        <button class="tab" onclick="showTab('colors-branding')">
            <i class="fas fa-palette me-2"></i>
            Colors & Branding
        </button>
        <button class="tab" onclick="showTab('social-media')">
            <i class="fas fa-share-alt me-2"></i>
            Social Media
        </button>
        <button class="tab" onclick="showTab('footer-content')">
            <i class="fas fa-align-left me-2"></i>
            Footer Content
        </button>
        <button class="tab" onclick="showTab('contact-page')">
            <i class="fas fa-envelope me-2"></i>
            Contact Page
        </button>
        <button class="tab" onclick="showTab('login-page')">
            <i class="fas fa-sign-in-alt me-2"></i>
            Login Page
        </button>
    </div>

    <!-- General Settings Tab -->
    <div id="general-settings" class="tab-content active">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_general">
            
            <h3 class="form-section-title">
                <i class="fas fa-info-circle me-2"></i>
                General Website Settings
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="site_name">Site Name</label>
                    <input type="text" id="site_name" name="site_name" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="site_tagline">Site Tagline</label>
                    <input type="text" id="site_tagline" name="site_tagline" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['site_tagline'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <input type="text" id="phone_number" name="phone_number" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['phone_number'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['email'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="address">Business Address</label>
                <textarea id="address" name="address" class="form-control" rows="3"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save General Settings
            </button>
        </form>
    </div>

    <!-- Colors & Branding Tab -->
    <div id="colors-branding" class="tab-content">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_colors">
            
            <h3 class="form-section-title">
                <i class="fas fa-palette me-2"></i>
                Colors & Branding
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="primary_color">Primary Color</label>
                    <div class="color-input-group">
                        <input type="color" id="primary_color_picker" class="color-picker"
                               value="<?php echo $settings['primary_color'] ?? '#1A1A1A'; ?>">
                        <input type="text" id="primary_color" name="primary_color" class="form-control color-text"
                               value="<?php echo $settings['primary_color'] ?? '#1A1A1A'; ?>"
                               onchange="document.getElementById('primary_color_picker').value = this.value">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="accent_color">Accent Color</label>
                    <div class="color-input-group">
                        <input type="color" id="accent_color_picker" class="color-picker"
                               value="<?php echo $settings['accent_color'] ?? '#E67E22'; ?>"
                               onchange="document.getElementById('accent_color').value = this.value">
                        <input type="text" id="accent_color" name="accent_color" class="form-control color-text"
                               value="<?php echo $settings['accent_color'] ?? '#E67E22'; ?>"
                               onchange="document.getElementById('accent_color_picker').value = this.value">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="secondary_color">Secondary Color</label>
                    <div class="color-input-group">
                        <input type="color" id="secondary_color_picker" class="color-picker"
                               value="<?php echo $settings['secondary_color'] ?? '#F5F5F5'; ?>"
                               onchange="document.getElementById('secondary_color').value = this.value">
                        <input type="text" id="secondary_color" name="secondary_color" class="form-control color-text"
                               value="<?php echo $settings['secondary_color'] ?? '#F5F5F5'; ?>"
                               onchange="document.getElementById('secondary_color_picker').value = this.value">
                    </div>
                </div>
            </div>

            <h4 class="mt-4 mb-3">Table Appearance</h4>
            <div class="form-grid">
                <div class="form-group">
                    <label for="table_header_bg">Table Header Background</label>
                    <div class="color-input-group">
                        <input type="color" id="table_header_bg_picker" class="color-picker"
                               value="<?php echo $settings['table_header_bg'] ?? '#f8f9fa'; ?>"
                               onchange="document.getElementById('table_header_bg').value = this.value">
                        <input type="text" id="table_header_bg" name="table_header_bg" class="form-control color-text"
                               value="<?php echo $settings['table_header_bg'] ?? '#f8f9fa'; ?>"
                               onchange="document.getElementById('table_header_bg_picker').value = this.value">
                    </div>
                </div>

                <div class="form-group">
                    <label for="table_header_text">Table Header Text Color</label>
                    <div class="color-input-group">
                        <input type="color" id="table_header_text_picker" class="color-picker"
                               value="<?php echo $settings['table_header_text'] ?? '#495057'; ?>"
                               onchange="document.getElementById('table_header_text').value = this.value">
                        <input type="text" id="table_header_text" name="table_header_text" class="form-control color-text"
                               value="<?php echo $settings['table_header_text'] ?? '#495057'; ?>"
                               onchange="document.getElementById('table_header_text_picker').value = this.value">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
            <div class="form-group">
                <label for="logo_file">Site Logo (Black Version)</label>
                <div class="logo-preview mb-3" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                    <img src="<?php echo getThemeOption('site_logo', themeUrl('images/logo.svg')); ?>"
                         alt="Site Logo Preview" class="logo-size-preview <?php echo 'logo-size-' . ($settings['logo_size'] ?? 'medium'); ?>">
                </div>
                <input type="file" id="logo_file" name="logo_file" class="form-control" accept=".jpg,.jpeg,.png,.svg">
                <small class="text-muted">Current: <?php echo basename($settings['site_logo'] ?? 'No logo uploaded'); ?></small>
                <small class="form-text text-muted">Used in website header and main areas</small>
            </div>
            <div class="form-group">
                <label for="logo_size">Logo Size</label>
                <select id="logo_size" name="logo_size" class="form-control">
                    <option value="small" <?php echo ($settings['logo_size'] ?? 'medium') === 'small' ? 'selected' : ''; ?>>Small</option>
                    <option value="medium" <?php echo ($settings['logo_size'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>Medium</option>
                    <option value="large" <?php echo ($settings['logo_size'] ?? 'medium') === 'large' ? 'selected' : ''; ?>>Large</option>
                </select>
                <small class="form-text text-muted">Controls logo size in header and footer. Preview above.</small>
            </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="white_logo_file">White Logo Version</label>
                        <div class="logo-preview mb-3" style="background: #2c3e50; padding: 20px; border-radius: 8px; text-align: center;">
                            <img src="<?php echo getThemeOption('site_logo_white', themeUrl('images/logo-white.svg')); ?>"
                                 alt="White Logo Preview" class="logo-size-preview logo-size-<?php echo $settings['logo_size'] ?? 'medium'; ?>">
                        </div>
                        <input type="file" id="white_logo_file" name="white_logo_file" class="form-control" accept=".jpg,.jpeg,.png,.svg">
                        <small class="text-muted">Current: <?php echo basename(getThemeOption('site_logo_white', 'logo-white.svg')); ?></small>
                        <small class="form-text text-muted">Used in footer and admin header</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="favicon_file">Favicon</label>
                        <div class="favicon-preview mb-3" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <img src="<?php echo getThemeOption('favicon', themeUrl('images/favicon.ico')); ?>"
                                 alt="Favicon Preview" style="width: 32px; height: 32px; image-rendering: pixelated;">
                            <div class="mt-2">
                                <small class="text-muted">32x32 pixels</small>
                            </div>
                        </div>
                        <input type="file" id="favicon_file" name="favicon_file" class="form-control" accept=".ico,.png,.svg">
                        <small class="text-muted">Current: <?php echo basename($settings['favicon'] ?? 'No favicon uploaded'); ?></small>
                        <small class="form-text text-muted">Browser tab icon</small>
                    </div>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Colors & Branding
            </button>
        </form>
    </div>

    <!-- Social Media Tab -->
    <div id="social-media" class="tab-content">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_social">
            
            <h3 class="form-section-title">
                <i class="fas fa-share-alt me-2"></i>
                Social Media Links
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="facebook_url">
                        <i class="fab fa-facebook me-2"></i>
                        Facebook URL
                    </label>
                    <input type="url" id="facebook_url" name="facebook_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['facebook_url'] ?? ''); ?>" 
                           placeholder="https://facebook.com/yourpage">
                </div>
                
                <div class="form-group">
                    <label for="twitter_url">
                        <i class="fab fa-twitter me-2"></i>
                        Twitter URL
                    </label>
                    <input type="url" id="twitter_url" name="twitter_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['twitter_url'] ?? ''); ?>" 
                           placeholder="https://twitter.com/yourhandle">
                </div>
                
                <div class="form-group">
                    <label for="linkedin_url">
                        <i class="fab fa-linkedin me-2"></i>
                        LinkedIn URL
                    </label>
                    <input type="url" id="linkedin_url" name="linkedin_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['linkedin_url'] ?? ''); ?>" 
                           placeholder="https://linkedin.com/company/yourcompany">
                </div>
                
                <div class="form-group">
                    <label for="instagram_url">
                        <i class="fab fa-instagram me-2"></i>
                        Instagram URL
                    </label>
                    <input type="url" id="instagram_url" name="instagram_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['instagram_url'] ?? ''); ?>" 
                           placeholder="https://instagram.com/yourhandle">
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Social Media Links
            </button>
        </form>
    </div>

    <!-- Footer Content Tab -->
    <div id="footer-content" class="tab-content">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_footer">
            
            <h3 class="form-section-title">
                <i class="fas fa-align-left me-2"></i>
                Footer Content
            </h3>
            
            <div class="form-group">
                <label for="footer_about_text">About Text</label>
                <textarea id="footer_about_text" name="footer_about_text" class="form-control" rows="4" 
                          placeholder="Brief description about your company..."><?php echo htmlspecialchars($settings['footer_about_text'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="footer_copyright">Copyright Text</label>
                <input type="text" id="footer_copyright" name="footer_copyright" class="form-control" 
                       value="<?php echo htmlspecialchars($settings['footer_copyright'] ?? ''); ?>" 
                       placeholder="All rights reserved.">
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Footer Content
            </button>
        </form>
    </div>

    <!-- Contact Page Tab -->
    <div id="contact-page" class="tab-content">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_contact">
            
            <h3 class="form-section-title">
                <i class="fas fa-envelope me-2"></i>
                Contact Page Settings
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="contact_hero_title">Hero Section Title</label>
                    <input type="text" id="contact_hero_title" name="contact_hero_title" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['contact_hero_title'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="contact_form_title">Contact Form Title</label>
                    <input type="text" id="contact_form_title" name="contact_form_title" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['contact_form_title'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="contact_hero_description">Hero Section Description</label>
                <textarea id="contact_hero_description" name="contact_hero_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['contact_hero_description'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="business_hours">Business Hours</label>
                <textarea id="business_hours" name="business_hours" class="form-control" rows="4"><?php echo htmlspecialchars($settings['business_hours'] ?? ''); ?></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Contact Page Settings
            </button>
        </form>
    </div>

    <!-- Login Page Tab -->
    <div id="login-page" class="tab-content">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_login_page">

            <h3 class="form-section-title">
                <i class="fas fa-sign-in-alt me-2"></i>
                Login Page Background Customization
            </h3>

            <div class="form-group">
                <label for="login_background_type">Background Type</label>
                <select id="login_background_type" name="login_background_type" class="form-control" onchange="toggleLoginBackgroundOptions()">
                    <option value="gradient" <?php echo ($settings['login_background_type'] ?? 'gradient') === 'gradient' ? 'selected' : ''; ?>>Animated Gradient</option>
                    <option value="solid" <?php echo ($settings['login_background_type'] ?? '') === 'solid' ? 'selected' : ''; ?>>Solid Color</option>
                    <option value="image" <?php echo ($settings['login_background_type'] ?? '') === 'image' ? 'selected' : ''; ?>>Background Image</option>
                    <option value="video" <?php echo ($settings['login_background_type'] ?? '') === 'video' ? 'selected' : ''; ?>>Background Video</option>
                </select>
            </div>

            <!-- Solid Color Options -->
            <div id="solid-color-options" class="background-option" style="display: none;">
                <div class="form-group">
                    <label for="login_background_color">Background Color</label>
                    <div class="color-input-group">
                        <input type="color" id="login_background_color_picker" class="color-picker"
                               value="<?php echo $settings['login_background_color'] ?? '#1A1A1A'; ?>"
                               onchange="document.getElementById('login_background_color').value = this.value">
                        <input type="text" id="login_background_color" name="login_background_color" class="form-control color-text"
                               value="<?php echo $settings['login_background_color'] ?? '#1A1A1A'; ?>"
                               onchange="document.getElementById('login_background_color_picker').value = this.value">
                    </div>
                </div>
            </div>

            <!-- Gradient Options -->
            <div id="gradient-options" class="background-option">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="login_background_gradient_start">Gradient Start Color</label>
                        <div class="color-input-group">
                            <input type="color" id="login_background_gradient_start_picker" class="color-picker"
                                   value="<?php echo $settings['login_background_gradient_start'] ?? '#1A1A1A'; ?>"
                                   onchange="document.getElementById('login_background_gradient_start').value = this.value">
                            <input type="text" id="login_background_gradient_start" name="login_background_gradient_start" class="form-control color-text"
                                   value="<?php echo $settings['login_background_gradient_start'] ?? '#1A1A1A'; ?>"
                                   onchange="document.getElementById('login_background_gradient_start_picker').value = this.value">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="login_background_gradient_end">Gradient End Color</label>
                        <div class="color-input-group">
                            <input type="color" id="login_background_gradient_end_picker" class="color-picker"
                                   value="<?php echo $settings['login_background_gradient_end'] ?? '#E67E22'; ?>"
                                   onchange="document.getElementById('login_background_gradient_end').value = this.value">
                            <input type="text" id="login_background_gradient_end" name="login_background_gradient_end" class="form-control color-text"
                                   value="<?php echo $settings['login_background_gradient_end'] ?? '#E67E22'; ?>"
                                   onchange="document.getElementById('login_background_gradient_end_picker').value = this.value">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="login_background_gradient_direction">Gradient Direction</label>
                    <select id="login_background_gradient_direction" name="login_background_gradient_direction" class="form-control">
                        <option value="45deg" <?php echo ($settings['login_background_gradient_direction'] ?? '45deg') === '45deg' ? 'selected' : ''; ?>>Diagonal (45°)</option>
                        <option value="90deg" <?php echo ($settings['login_background_gradient_direction'] ?? '') === '90deg' ? 'selected' : ''; ?>>Vertical (90°)</option>
                        <option value="0deg" <?php echo ($settings['login_background_gradient_direction'] ?? '') === '0deg' ? 'selected' : ''; ?>>Horizontal (0°)</option>
                        <option value="135deg" <?php echo ($settings['login_background_gradient_direction'] ?? '') === '135deg' ? 'selected' : ''; ?>>Diagonal (135°)</option>
                        <option value="radial" <?php echo ($settings['login_background_gradient_direction'] ?? '') === 'radial' ? 'selected' : ''; ?>>Radial</option>
                    </select>
                </div>
            </div>

            <!-- Image Options -->
            <div id="image-options" class="background-option" style="display: none;">
                <div class="form-group">
                    <label for="login_background_image">Background Image</label>
                    <?php if (!empty($settings['login_background_image'])): ?>
                        <div class="image-preview mb-3" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <img src="<?php echo $settings['login_background_image']; ?>"
                                 alt="Login Background Preview" style="max-height: 200px; max-width: 100%; border-radius: 8px;">
                        </div>
                    <?php endif; ?>
                    <input type="file" id="login_background_image" name="login_background_image" class="form-control" accept=".jpg,.jpeg,.png,.webp">
                    <small class="form-text text-muted">Recommended: High resolution image (1920x1080 or larger)</small>
                </div>
            </div>

            <!-- Video Options -->
            <div id="video-options" class="background-option" style="display: none;">
                <div class="form-group">
                    <label for="login_background_video">Background Video</label>
                    <?php if (!empty($settings['login_background_video'])): ?>
                        <div class="video-preview mb-3" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <video controls style="max-height: 200px; max-width: 100%; border-radius: 8px;">
                                <source src="<?php echo $settings['login_background_video']; ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    <?php endif; ?>
                    <input type="file" id="login_background_video" name="login_background_video" class="form-control" accept=".mp4,.webm,.ogg">
                    <small class="form-text text-muted">Recommended: MP4 format, under 50MB for best performance</small>
                </div>
            </div>

            <!-- Common Options -->
            <h4 class="mt-4 mb-3">Additional Settings</h4>
            <div class="form-grid">
                <div class="form-group">
                    <label for="login_overlay_opacity">Background Overlay Opacity</label>
                    <div class="range-input-group">
                        <input type="range" id="login_overlay_opacity" name="login_overlay_opacity"
                               class="form-range" min="0" max="1" step="0.1"
                               value="<?php echo $settings['login_overlay_opacity'] ?? '0.7'; ?>"
                               oninput="document.getElementById('login_overlay_opacity_value').textContent = this.value">
                        <span class="range-value" id="login_overlay_opacity_value"><?php echo $settings['login_overlay_opacity'] ?? '0.7'; ?></span>
                    </div>
                    <small class="form-text text-muted">Controls the darkness of the overlay on background (0 = transparent, 1 = opaque)</small>
                </div>

                <div class="form-group">
                    <label for="login_overlay_color">Background Overlay Color</label>
                    <div class="color-input-group">
                        <input type="color" id="login_overlay_color_picker" class="color-picker"
                               value="<?php echo $settings['login_overlay_color'] ?? '#000000'; ?>"
                               onchange="document.getElementById('login_overlay_color').value = this.value">
                        <input type="text" id="login_overlay_color" name="login_overlay_color" class="form-control color-text"
                               value="<?php echo $settings['login_overlay_color'] ?? '#000000'; ?>"
                               onchange="document.getElementById('login_overlay_color_picker').value = this.value">
                    </div>
                    <small class="form-text text-muted">Color of the overlay applied over the background</small>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="login_particles_enabled" name="login_particles_enabled"
                               class="form-check-input" value="1"
                               <?php echo ($settings['login_particles_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>
                               onchange="toggleParticlesOptions()">
                        <label for="login_particles_enabled" class="form-check-label">
                            Enable Floating Particles
                        </label>
                    </div>
                    <small class="form-text text-muted">Show animated floating particles in the background</small>
                </div>

                <div id="particles-options" class="form-group" style="<?php echo ($settings['login_particles_enabled'] ?? '1') === '1' ? '' : 'display: none;'; ?>">
                    <label for="login_particles_url">Custom Particles JSON/URL (Optional)</label>
                    <input type="url" id="login_particles_url" name="login_particles_url" class="form-control"
                           value="<?php echo $settings['login_particles_url'] ?? ''; ?>"
                           placeholder="https://example.com/particles.json or leave empty for default">
                    <small class="form-text text-muted">
                        <strong>Options:</strong><br>
                        • Leave empty for default CSS particles<br>
                        • Enter a URL to a particles.js JSON configuration<br>
                        • Or use a CDN link to a particles library<br>
                        <a href="https://particles.js.org/" target="_blank" class="text-primary">Learn about particles.js</a>
                    </small>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="login_animation_enabled" name="login_animation_enabled"
                               class="form-check-input" value="1"
                               <?php echo ($settings['login_animation_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                        <label for="login_animation_enabled" class="form-check-label">
                            Enable Background Animation
                        </label>
                    </div>
                    <small class="form-text text-muted">Enable gradient animation and other background effects</small>
                </div>
            </div>

            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Preview:</strong> Changes will be visible on the admin login page after saving.
                <a href="login.php" target="_blank" class="alert-link">View Login Page</a>
            </div>

            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Login Page Settings
            </button>
        </form>
    </div>
</div>

<script>
// Handle file uploads for logos and favicon
document.addEventListener('DOMContentLoaded', function() {
    // Logo file upload
    const logoFile = document.getElementById('logo_file');
    if (logoFile) {
        logoFile.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadFile(this, 'upload_logo');
            }
        });
    }

    // White logo file upload
    const whiteLogoFile = document.getElementById('white_logo_file');
    if (whiteLogoFile) {
        whiteLogoFile.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadFile(this, 'upload_white_logo');
            }
        });
    }

    // Favicon file upload
    const faviconFile = document.getElementById('favicon_file');
    if (faviconFile) {
        faviconFile.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadFile(this, 'upload_favicon');
            }
        });
    }

    function uploadFile(fileInput, action) {
        const formData = new FormData();
        formData.append(fileInput.name, fileInput.files[0]);
        formData.append('action', action);

        // Show loading state
        const label = fileInput.previousElementSibling;
        const originalText = label.textContent;
        label.textContent = 'Uploading...';
        fileInput.disabled = true;

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // Reload the page to show the result
            window.location.reload();
        })
        .catch(error => {
            console.error('Upload error:', error);
            label.textContent = originalText;
            fileInput.disabled = false;
            alert('Upload failed. Please try again.');
        });
    }

    // Initialize login background options on page load
    toggleLoginBackgroundOptions();
});

// Function to toggle login background options based on selected type
function toggleLoginBackgroundOptions() {
    const backgroundType = document.getElementById('login_background_type').value;
    const options = document.querySelectorAll('.background-option');

    // Hide all options first
    options.forEach(option => {
        option.style.display = 'none';
    });

    // Show relevant option
    switch(backgroundType) {
        case 'solid':
            document.getElementById('solid-color-options').style.display = 'block';
            break;
        case 'gradient':
            document.getElementById('gradient-options').style.display = 'block';
            break;
        case 'image':
            document.getElementById('image-options').style.display = 'block';
            break;
        case 'video':
            document.getElementById('video-options').style.display = 'block';
            break;
    }
}

// Function to toggle particles options
function toggleParticlesOptions() {
    const particlesEnabled = document.getElementById('login_particles_enabled').checked;
    const particlesOptions = document.getElementById('particles-options');

    if (particlesEnabled) {
        particlesOptions.style.display = 'block';
    } else {
        particlesOptions.style.display = 'none';
    }
}
</script>
