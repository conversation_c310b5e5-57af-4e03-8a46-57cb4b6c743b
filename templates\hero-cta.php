<?php
/**
 * Reusable Hero CTA Template
 * Can be used across different pages with custom content
 * Enhanced with dynamic styling controls
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Include hero page detection functions
require_once __DIR__ . '/../includes/hero-page-detection.php';

// Determine page name for database lookup
$current_page = isset($hero_page_name) ? $hero_page_name : 'home';

// Get hero section from database or use custom data
$db_hero = getHeroSection($current_page);

// Use custom data if provided, otherwise use database data, then fallback to defaults
$hero_caption = isset($hero_data['caption']) ? $hero_data['caption'] :
                ($db_hero ? $db_hero['caption'] : getThemeOption('hero_cta_caption', "Ready to Build?"));

$hero_title = isset($hero_data['title']) ? $hero_data['title'] :
              ($db_hero ? $db_hero['title'] : getThemeOption('hero_cta_title', 'Ready to Get Started?'));

$hero_description = isset($hero_data['description']) ? $hero_data['description'] :
                    ($db_hero ? $db_hero['description'] : getThemeOption('hero_cta_description', "Let's transform your vision into reality with our innovative architectural solutions and expert craftsmanship."));

$hero_button_text = isset($hero_data['button_text']) ? $hero_data['button_text'] :
                    ($db_hero ? $db_hero['button_text'] : getThemeOption('hero_cta_button_text', 'Start Your Project'));

$hero_button_link = isset($hero_data['button_link']) ? $hero_data['button_link'] :
                    ($db_hero ? $db_hero['button_link'] : getThemeOption('hero_cta_button_link', 'contact'));

// Newsletter signup settings
$show_newsletter = $db_hero ? ($db_hero['show_newsletter_input'] ?? false) : false;
$newsletter_placeholder = $db_hero ? ($db_hero['newsletter_placeholder'] ?? 'Enter your email address') : 'Enter your email address';
$newsletter_button_text = $db_hero ? ($db_hero['newsletter_button_text'] ?? 'Subscribe') : 'Subscribe';
$newsletter_success_message = $db_hero ? ($db_hero['newsletter_success_message'] ?? 'Thank you for subscribing!') : 'Thank you for subscribing!';

// Handle background and styling based on database settings or custom data
$background_style = '';
$background_class = 'cta-section';

// Get styling options from database hero section
$height_style = '';
$text_colors = [];
$button_colors = [];

if ($db_hero) {
    // Height styling
    $height_value = getHeroHeight($db_hero['height_type'] ?? 'medium', $db_hero['height_custom'] ?? null);
    $height_style = "min-height: $height_value;";

    // Text colors
    $text_colors = [
        'caption' => $db_hero['caption_color'] ?? '#ffffff',
        'title' => $db_hero['title_color'] ?? '#ffffff',
        'description' => $db_hero['description_color'] ?? '#ffffff'
    ];

    // Button colors
    $button_colors = [
        'background' => $db_hero['button_bg_color'] ?? '#E67E22',
        'text' => $db_hero['button_text_color'] ?? '#ffffff',
        'hover_bg' => $db_hero['button_hover_bg_color'] ?? '#d35400'
    ];

    // Background opacity and overlay color
    $bg_opacity = $db_hero['background_opacity'] ?? 0.60;
    $overlay_color = $db_hero['background_color'] ?? '#000000';
} else {
    // Fallback values
    $height_style = 'min-height: 400px;';
    $text_colors = [
        'caption' => '#ffffff',
        'title' => '#ffffff',
        'description' => '#ffffff'
    ];
    $button_colors = [
        'background' => '#E67E22',
        'text' => '#ffffff',
        'hover_bg' => '#d35400'
    ];
    $bg_opacity = 0.60;
    $overlay_color = '#000000';
}

// Convert hex color to RGB for overlay
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2))
    ];
}

$overlay_rgb = hexToRgb($overlay_color);
$overlay_style = "background: rgba({$overlay_rgb['r']}, {$overlay_rgb['g']}, {$overlay_rgb['b']}, $bg_opacity);";

if ($db_hero) {
    if ($db_hero['background_type'] === 'image' && !empty($db_hero['background_image'])) {
        $background_style = "background-image: url('" . ensureAbsoluteUrl($db_hero['background_image']) . "'); background-size: cover; background-position: center;";
        $background_class .= ' cta-image-bg';
    } else {
        // Use gradient background - no additional overlay needed for gradients
        $gradient = $db_hero['background_gradient'] ?: "background: rgba({$overlay_rgb['r']}, {$overlay_rgb['g']}, {$overlay_rgb['b']}, $bg_opacity);";
        $background_style = "background: $gradient;";
        $background_class .= ' cta-gradient-bg';
    }
} elseif (isset($hero_data['background']) && !empty($hero_data['background'])) {
    $background_style = "background-image: url('" . $hero_data['background'] . "'); background-size: cover; background-position: center;";
    $background_class .= ' cta-image-bg';
} else {
    // Default solid color background
    $background_style = "background: rgba({$overlay_rgb['r']}, {$overlay_rgb['g']}, {$overlay_rgb['b']}, $bg_opacity);";
    $background_class .= ' cta-color-bg';
}
?>

<!-- Dynamic Hero CTA Section -->
<section class="<?php echo $background_class; ?>" style="<?php echo $background_style; ?> <?php echo $height_style; ?> padding: 6rem 0; position: relative; color: white; display: flex; align-items: center;">
    <!-- Overlay for image backgrounds only -->
    <?php if (strpos($background_class, 'cta-image-bg') !== false): ?>
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; <?php echo $overlay_style; ?> z-index: 1;"></div>
    <?php endif; ?>

    <div class="container" style="position: relative; z-index: 2; width: 100%;">
        <div class="hero-content" style="text-align: center; max-width: 600px; margin: 0 auto;">
            <?php if (!empty($hero_caption)): ?>
            <div class="caption" style="color: <?php echo $text_colors['caption']; ?>; font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 1px;">
                <?php echo htmlspecialchars($hero_caption); ?>
            </div>
            <?php endif; ?>

            <h2 class="hero-title" style="color: <?php echo $text_colors['title']; ?>; font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700;">
                <?php echo htmlspecialchars($hero_title); ?>
            </h2>

            <?php if (!empty($hero_description)): ?>
            <p class="hero-subtitle" style="color: <?php echo $text_colors['description']; ?>; font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; line-height: 1.6;">
                <?php echo htmlspecialchars($hero_description); ?>
            </p>
            <?php endif; ?>

            <?php if ($show_newsletter): ?>
            <!-- Newsletter Signup Form -->
            <form class="hero-newsletter-form" id="heroNewsletterForm" method="POST" action="<?php echo siteUrl('newsletter-signup'); ?>">
                <div class="newsletter-input-group">
                    <input type="email"
                           name="email"
                           class="newsletter-hero-input"
                           placeholder="<?php echo htmlspecialchars($newsletter_placeholder); ?>"
                           required>
                    <button type="submit" class="newsletter-hero-button">
                        <?php echo htmlspecialchars($newsletter_button_text); ?>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M5 12h14M12 5l7 7-7 7"/>
                        </svg>
                    </button>
                </div>
                <div class="newsletter-success-message" id="heroNewsletterSuccessMessage" style="display: none;">
                    <div class="success-icon">✅</div>
                    <div class="success-text"><?php echo htmlspecialchars($newsletter_success_message); ?></div>
                </div>
                <input type="hidden" name="source" value="hero_cta">
                <input type="hidden" name="page" value="<?php echo $current_page; ?>">
            </form>
            <?php else: ?>
            <!-- Regular Button -->
            <a href="<?php
                // Handle external URLs, phone numbers, and email addresses
                if (strpos($hero_button_link, 'http') === 0 ||
                    strpos($hero_button_link, 'tel:') === 0 ||
                    strpos($hero_button_link, 'mailto:') === 0 ||
                    strpos($hero_button_link, '#') === 0) {
                    echo $hero_button_link;
                } else {
                    echo siteUrl($hero_button_link);
                }
            ?>"
               class="btn btn-primary hero-cta-button"
               style="display: inline-flex; align-items: center; gap: 0.5rem;
                      background: <?php echo $button_colors['background']; ?>;
                      color: <?php echo $button_colors['text']; ?>;
                      padding: 1rem 2rem; border-radius: 4px; text-decoration: none;
                      font-weight: 500; transition: all 0.3s ease;"
               onmouseover="this.style.background='<?php echo $button_colors['hover_bg']; ?>'"
               onmouseout="this.style.background='<?php echo $button_colors['background']; ?>'">
                <?php echo htmlspecialchars($hero_button_text); ?>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                </svg>
            </a>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- POPUP SUCCESS MESSAGE (Impossible to miss) -->
<div class="hero-popup-overlay" id="heroPopupOverlay" style="display: none;"></div>
<div class="hero-popup-success" id="heroPopupSuccess" style="display: none;">
    <div class="popup-icon">🎉</div>
    <div class="popup-title">Success!</div>
    <div class="popup-message" id="heroPopupMessage"><?php echo htmlspecialchars($newsletter_success_message ?? 'Thank you for subscribing!'); ?></div>
    <div class="popup-close-hint">Click anywhere to close</div>
</div>

<style>
/* Newsletter Form Styles for Hero CTA */
.hero-newsletter-form {
    max-width: 450px;
    margin: 0 auto;
}

.newsletter-input-group {
    display: flex;
    gap: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    padding: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-hero-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: white;
    font-size: 16px;
    border-radius: 50px;
    outline: none;
}

.newsletter-hero-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-hero-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: <?php echo $button_colors['background']; ?>;
    color: <?php echo $button_colors['text']; ?>;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.newsletter-hero-button:hover {
    background: <?php echo $button_colors['hover_bg']; ?>;
    transform: translateX(2px);
}

.newsletter-success-message {
    margin-top: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 12px;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    animation: successFadeIn 0.5s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.success-icon {
    font-size: 1.2em;
    animation: successBounce 0.6s ease-out;
}

.success-text {
    font-size: 1.1em;
}

@keyframes successFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes successBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* POPUP SUCCESS MESSAGE STYLES */
.hero-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    animation: overlayFadeIn 0.3s ease-out;
}

.hero-popup-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 40px 50px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.4);
    z-index: 10001;
    text-align: center;
    animation: popupSlideIn 0.5s ease-out;
    min-width: 350px;
    max-width: 90vw;
}

.popup-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: iconBounce 0.8s ease-out;
}

.popup-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.popup-message {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    line-height: 1.4;
}

.popup-close-hint {
    font-size: 14px;
    opacity: 0.8;
    font-style: italic;
}

@keyframes overlayFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1) translateY(0);
    }
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .newsletter-input-group {
        flex-direction: column;
        border-radius: 12px;
        gap: 8px;
    }

    .newsletter-hero-input,
    .newsletter-hero-button {
        border-radius: 8px;
    }

    .newsletter-hero-button {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('heroNewsletterForm');
    const successMessage = document.getElementById('heroNewsletterSuccessMessage');

    if (form && successMessage) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const sourceInput = this.querySelector('input[name="source"]');
            const pageInput = this.querySelector('input[name="page"]');
            
            console.log('Form data:', {
                email: email,
                source: sourceInput ? sourceInput.value : 'unknown',
                page: pageInput ? pageInput.value : 'unknown'
            });

            if (email) {
                // Add loading state to button
                const button = this.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                button.innerHTML = 'Subscribing...';
                button.disabled = true;

                // Create FormData manually to ensure proper formatting
                const formData = new FormData();
                formData.append('email', email);
                formData.append('source', sourceInput ? sourceInput.value : 'hero_cta');
                formData.append('page', pageInput ? pageInput.value : 'unknown');

                console.log('Sending POST request to:', this.action);

                // Send AJAX request to newsletter signup
                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    console.log('Hero CTA response status:', response.status);
                    console.log('Hero CTA response headers:', response.headers);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Hero CTA raw response:', text);
                    
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error('Invalid JSON response');
                    }
                    
                    console.log('Hero CTA parsed response:', data);
                    
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;

                    if (data.success) {
                        // Update success message with database content or response message
                        const successText = successMessage.querySelector('.success-text');
                        if (successText) {
                            successText.textContent = data.message || '<?php echo addslashes($newsletter_success_message); ?>';
                        }

                        console.log('Hero CTA: SUCCESS! Showing popup message');

                        // Show POPUP success message (impossible to miss)
                        const popupOverlay = document.getElementById('heroPopupOverlay');
                        const popupSuccess = document.getElementById('heroPopupSuccess');
                        const popupMessage = document.getElementById('heroPopupMessage');

                        if (popupOverlay && popupSuccess && popupMessage) {
                            // Update popup message
                            popupMessage.textContent = data.message || '<?php echo addslashes($newsletter_success_message); ?>';

                            // Show popup
                            popupOverlay.style.display = 'block';
                            popupSuccess.style.display = 'block';

                            console.log('Hero CTA: Popup displayed');

                            // Close popup when clicking anywhere
                            function closePopup() {
                                popupOverlay.style.display = 'none';
                                popupSuccess.style.display = 'none';
                                console.log('Hero CTA: Popup closed');
                            }

                            popupOverlay.addEventListener('click', closePopup);
                            popupSuccess.addEventListener('click', closePopup);

                            // Auto-close after 5 seconds
                            setTimeout(closePopup, 5000);
                        }

                        // Also show inline success message
                        this.style.display = 'none';
                        successMessage.style.display = 'block';

                        // Reset form after 5 seconds
                        setTimeout(() => {
                            this.style.display = 'block';
                            successMessage.style.display = 'none';
                            this.reset();
                            console.log('Hero CTA: Form reset complete');
                        }, 5000);
                    } else {
                        // Show error message
                        console.error('Hero CTA error:', data.message);
                        alert(data.message || 'An error occurred. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Hero CTA newsletter signup error:', error);
                    
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                    
                    alert('An error occurred. Please try again.');
                });
            }
        });
    } else {
        console.log('Hero newsletter form not found on this page');
    }
});
</script>
